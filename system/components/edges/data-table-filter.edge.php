@props([
    'title' => 'PopoverBox',
    'description' => 'Basic form select',
    'label' => '',
    'name' => '',
    'class_suffix' => '',
    'col' => [],
    'sort_col' => '',
    'sort_dir' => '',
    'callback' => null,
    'id_count' => 0,
])
@php
 //   print_rr($col,'colly');
  //  print_rr($callback,'callyback');
    $in_sort_col = is_array($col['field']) ? $col['field'][0] : $col['field'];

    // Get the current filter values from the request
//    $current_from = INPUT_PARAMS['column'][$col['field']]['from'];
//    $current_to = INPUT_PARAMS['column'][$col['field'] ]['to'];
    $current_multi_filter = INPUT_PARAMS['column'][$in_sort_col]['multi'];
  //  print_rr(INPUT_PARAMS,'INPUT_PARAMS');
    if ($sort_col == $in_sort_col) {
        $sort_up = 'group sort-up size-5 ' . ($sort_dir == 'DESC' ? 'visible' : 'hidden');
        $sort_down = 'group sort-down size-5 ' . ($sort_dir == 'ASC' ? 'visible' : 'hidden');
    } else {
        $sort_up = 'group sort-up size-5 hidden';
        $sort_down = 'group sort-down size-5 invisible group-hover:visible';
    }
    $col['field'] = is_array($col['field']) ? $col['field'][0] : $col['field'];
@endphp

<div class="relative"
     x-data="{
            showControls_{{ $id_count }}: false,
            open_{{ $id_count }}: false
     }"
     @keydown.escape="onEscape"
     @close-popover-group.window="onClosePopoverGroup">

    <div class="group relative inline-flex items-center">
        <button type="button"
                class="{{ !empty($current_filter) || !empty($current_from) || !empty($current_to) ? 'text-blue-600 font-semibold' : '' }}"
                @click="open_{{ $id_count }} = !open_{{ $id_count }}">
            <span>{{ $label }}</span>
            @if(!empty($current_multi_filter))
                <span class="ml-1 text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full">●</span>
            @endif
        </button>
         <div class="flex items-center ml-1 whitespace-nowrap z-30">
            @php
                if ($sort_col == $col['field']){
                    $order_direction = $sort_dir == 'ASC' ? 'DESC' : 'ASC';
                } else {
                    $order_direction = 'ASC';
                }
            @endphp
            <button id="{{ $col['field'] }}_sort"
                    class="absolute rounded-sm text-gray-900 group-hover:bg-gray-200"
                    hx-post="{{ APP_ROOT . DIRECTORY_SEPARATOR . "api/data_table/data_table_filter" }}"
                    hx-target=".data_table"
                    hx-swap="outerHTML"
                    hx-include=".data_table_filter"
                    hx-vals='{{ json_encode(['order_by' => $col['field'],'order_direction' => $order_direction,'callback' => $callback]) }}'
            >
                @icon('mini-chevron-up',$sort_up,['x-ref' => 'sortUp' ])
                @icon('mini-chevron-down', $sort_down,['x-ref' => 'sortDown'])
            </button>
        </div>
    </div>

    <div x-show="open_{{ $id_count }}"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 translate-y-1"
         x-transition:enter-end="opacity-100 translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 translate-y-0"
         x-transition:leave-end="opacity-0 translate-y-1"
         class="fixed z-10"
         style="display: none"
         x-ref="panel"
         @click.away="open_{{ $id_count }} = false">

        <div class="w-screen max-w-sm flex-auto overflow-hidden rounded-3xl bg-white text-sm/6 ring-1 shadow-lg ring-gray-900/5 lg:max-w-3xl">
            <div class="gap-x-6 gap-y-1 p-4 lg:grid-cols-1">
                @if (is_array($col['filters']))
                    <x-component-multi-select
                            :id="col_{{ $col['field'] ?? strtolower(str_replace([' '],'',$col['label'])) }}_filter"
                            :name="column[{{ $col['field'] }}][multi]"
                            class_suffix="max-w-42 data_table_filter"
                            :label="$col['label']"
                            :options="$col['filters']"
                            :selected="$current_multi_filter"
                            hx-include=".data_table_filter"
                            :hx-post="{{ APP_ROOT . DIRECTORY_SEPARATOR . "api/data_table/data_table_filter" }}"
                            hx-target=".data_table"
                            hx-swap="outerHTML"
                    ></x-component-multi-select>
                @endif
            </div>
            <div class="bg-gray-50 px-8 py-6 flex justify-between">
                <x-forms-button
                    label="Clear"
                    variant="secondary"
                    @click='$refs.panel.querySelectorAll("input").forEach(input => input.value = "")'
                ></x-forms-button>
                <x-forms-button
                    label="Apply"
                    variant="primary"
                    hx-include=".data_table_filter"
                    :hx-post="{{ APP_ROOT . DIRECTORY_SEPARATOR . "api/data_table/data_table_filter" }}"
                    hx-target=".data_table"
                    hx-swap="outerHTML"
                    :hx-vals='json_encode(["callback"=>$callback])'
                ></x-forms-button>
            </div>
        </div>
    </div>
</div>