@props([
    'columns' => [],
    'available_fields' => []
])

@php
// Convert columns to the format expected by Alpine.js
$processed_columns = [];
foreach ($columns as $index => $col) {
    $column_id = 'col_' . $index . '_' . md5($col['label']);
    $processed_columns[] = [
        'id' => $column_id,
        'label' => $col['label'],
        'field' => $col['key'],
        'filter' => true,
        'fields' => [$col['key']], // Default to one field per column from data source
        'parent' => null
    ];
}
@endphp

<!-- Data for Alpine.js -->
<script type="application/json" id="column-data">
{
    "columns": {!! json_encode($processed_columns) !!},
    "fields": {!! json_encode($available_fields) !!}
}
</script>

<div x-data="{
    newColumns: JSON.parse(document.getElementById('column-data').textContent).columns,
    newAvailableFields: JSON.parse(document.getElementById('column-data').textContent).fields
}"
x-init="
    // Update parent component data
    columns = newColumns;
    availableFields = newAvailableFields;
    hiddenColumns = [];
    $nextTick(() => {
        // Initialize sortable for columns
        const container = $refs.columnList;
        if (container && window.Sortable) {
            new Sortable(container, {
                animation: 150,
                handle: '.column-drag-handle',
                filter: '.field-item',
                onEnd: (evt) => {
                    const newOrder = [];
                    const items = container.querySelectorAll('[data-column-id]');
                    items.forEach(item => {
                        const columnId = item.dataset.columnId;
                        const column = columns.find(col => col.id === columnId);
                        if (column) {
                            newOrder.push(column);
                        }
                    });
                    columns = newOrder;
                }
            });

            // Initialize sortable for fields within columns
            container.querySelectorAll('.field-container').forEach(fieldContainer => {
                new Sortable(fieldContainer, {
                    group: 'fields',
                    animation: 150,
                    handle: '.field-drag-handle',
                    onEnd: (evt) => {
                        const fieldName = evt.item.dataset.fieldName;
                        const targetColumnId = evt.to.closest('[data-column-id]').dataset.columnId;
                        const sourceColumnId = evt.from.closest('[data-column-id]').dataset.columnId;

                        if (targetColumnId !== sourceColumnId) {
                            // Move field between columns
                            const sourceCol = columns.find(col => col.id === sourceColumnId);
                            const targetCol = columns.find(col => col.id === targetColumnId);

                            if (sourceCol && targetCol) {
                                const fieldIndex = sourceCol.fields.indexOf(fieldName);
                                if (fieldIndex > -1) {
                                    sourceCol.fields.splice(fieldIndex, 1);
                                    if (!targetCol.fields.includes(fieldName)) {
                                        targetCol.fields.push(fieldName);
                                    }
                                }
                            }
                        }
                    }
                });
            });
        }
    });
">
    <div x-ref="columnList" class="space-y-3">
        <template x-for="column in columns" :key="column.id">
            <div class="border border-gray-200 rounded-lg bg-white"
                 :data-column-id="column.id">

                <!-- Column Header -->
                <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">
                    <!-- Column Drag Handle -->
                    <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                        </svg>
                    </div>

                    <!-- Column Visibility Toggle -->
                    <button type="button"
                            @click="toggleColumn(column.id)"
                            class="mr-2 text-sm"
                            :class="isColumnVisible(column.id) ? 'text-green-600' : 'text-gray-400'">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  :d="isColumnVisible(column.id) ? 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z' : 'M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21'" />
                        </svg>
                    </button>

                    <!-- Column Label -->
                    <div class="flex-1">
                        <div class="font-medium text-sm text-gray-900" x-text="column.label"></div>
                        <div class="text-xs text-gray-500" x-text="column.fields.length + ' field(s)'"></div>
                    </div>

                    <!-- Add Field Dropdown -->
                    <div class="relative ml-2" x-data="{ showDropdown: false }">
                        <button type="button"
                                @click="showDropdown = !showDropdown"
                                class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                            + Field
                        </button>
                        <div x-show="showDropdown"
                             @click.away="showDropdown = false"
                             x-transition
                             class="absolute right-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10 max-h-48 overflow-y-auto">
                            <template x-for="field in newAvailableFields" :key="field">
                                <button type="button"
                                        @click="
                                            const col = columns.find(c => c.id === column.id);
                                            if (col && !col.fields.includes(field)) {
                                                col.fields.push(field);
                                            }
                                            showDropdown = false;
                                        "
                                        x-show="!column.fields.includes(field)"
                                        class="block w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-100"
                                        x-text="field">
                                </button>
                            </template>
                        </div>
                    </div>

                    <!-- Remove Column Button -->
                    <button type="button"
                            @click="removeColumn(column.id)"
                            class="ml-2 text-gray-400 hover:text-red-600">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                </div>

                <!-- Column Fields -->
                <div class="p-3">
                    <div class="field-container min-h-[2rem] border-2 border-dashed border-gray-200 rounded p-2"
                         :data-column-id="column.id">
                        <template x-for="field in column.fields" :key="field">
                            <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                 :data-field-name="field">
                                <!-- Field Drag Handle -->
                                <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                    </svg>
                                </div>
                                <span class="text-gray-700" x-text="field"></span>
                                <!-- Remove Field Button -->
                                <button type="button"
                                        @click="
                                            const col = columns.find(c => c.id === column.id);
                                            if (col) {
                                                const fieldIndex = col.fields.indexOf(field);
                                                if (fieldIndex > -1) {
                                                    col.fields.splice(fieldIndex, 1);
                                                }
                                            }
                                        "
                                        class="text-gray-400 hover:text-red-600 ml-1">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </template>

                        <!-- Drop Zone Indicator -->
                        <div class="text-xs text-gray-400 text-center py-2"
                             x-show="column.fields.length === 0">
                            Drop fields here or use dropdown above
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</div>
