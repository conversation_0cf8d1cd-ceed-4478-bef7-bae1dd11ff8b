@props([
        'form_title' => 'Field Definition',
        'field_name' => 'new_field',
        'field' => [],
        'types' => ['text', 'number', 'date', 'boolean'],
        'categories' => ['customer', 'subscription', 'invoice', 'payment', 'custom'],
        'submit_action' => 'create',
        
])
@use system\unified_field_definitions

        <!-- Form -->
        <form id="field-definition-form"
              class="mt-6"
              hx-post="{{ APP_ROOT }}/api/unified_field_definitions/{{ $submit_action }}"
              hx-target="#modal_body"
              hx-swap="innerHTML"
              hx-encoding="multipart/form-data">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Left Column: Basic Information -->
                <div class="space-y-4">
                    <h4 class="text-md font-medium text-gray-900 border-b pb-2">Basic Information</h4>
                    
                    <!-- Field Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Field Name</label>
                        <input type="text" 
                               name="field_name" 
                               value="{{ htmlspecialchars($is_new ? '' : $field_name) }}"
                               {{ $is_new ? '' : 'readonly' }}
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 {{ $is_new ? '' : 'bg-gray-50' }}"
                               required>
                        @if(!$is_new)
                            <p class="text-xs text-gray-500 mt-1">Field name cannot be changed after creation</p>
                        @endif
                    </div>

                    <!-- Label -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Label</label>
                        <input type="text" 
                               name="label" 
                               value="{{ htmlspecialchars($field['label'] ?? '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                               required>
                    </div>

                    <!-- Type -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                        <select name="type" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                required>
                            @foreach($types as $type)
                                <option value="{{ $type }}" {{ ($field['type'] ?? '') === $type ? 'selected' : '' }}>
                                    {{ ucfirst($type) }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Category -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <select name="category" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                required>
                            @foreach($categories as $category)
                                <option value="{{ $category }}" {{ ($field['category'] ?? '') === $category ? 'selected' : '' }}>
                                    {{ ucwords(str_replace('_', ' ', $category)) }}
                                </option>
                            @endforeach
                            <option value="custom" {{ ($field['category'] ?? '') === 'custom' ? 'selected' : '' }}>Custom</option>
                        </select>
                    </div>

                    <!-- Description -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea name="description" 
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                  required>{{ htmlspecialchars($field['description'] ?? '') }}</textarea>
                    </div>
                </div>

                <!-- Right Column: Advanced Configuration -->
                <div class="space-y-4">
                    <h4 class="text-md font-medium text-gray-900 border-b pb-2">Field Patterns & Mapping</h4>
                    
                    <!-- Field Patterns -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Field Patterns</label>
                        <textarea name="patterns" 
                                  rows="4"
                                  placeholder="Enter field patterns, one per line"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                  required>{{ implode("\n", $field['patterns'] ?? []) }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Field name variations that should map to this field</p>
                    </div>

                    <!-- Normalized Fields -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Normalized Fields</label>
                        <textarea name="normalized_fields" 
                                  rows="3"
                                  placeholder="Enter normalized field names, one per line"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                  required>{{ implode("\n", $field['normalized_fields'] ?? []) }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Standard field names this field should map to</p>
                    </div>

                    <!-- Matching Configuration -->
                    <div class="border border-gray-200 rounded-md p-4">
                        <h5 class="text-sm font-medium text-gray-900 mb-3">Matching Configuration</h5>
                        
                        <div class="space-y-3">
                            <!-- Enabled for Matching -->
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       name="matching_enabled" 
                                       id="matching_enabled"
                                       {{ ($field['matching']['enabled'] ?? false) ? 'checked' : '' }}
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="matching_enabled" class="ml-2 text-sm text-gray-700">
                                    Enable for subscription matching
                                </label>
                            </div>

                            <!-- Priority -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                                <input type="number" 
                                       name="matching_priority" 
                                       value="{{ $field['matching']['priority'] ?? 10 }}"
                                       min="0"
                                       max="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <p class="text-xs text-gray-500 mt-1">Lower numbers = higher priority</p>
                            </div>

                            <!-- Fuzzy Matching -->
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       name="fuzzy_matching" 
                                       id="fuzzy_matching"
                                       {{ ($field['matching']['fuzzy_matching'] ?? false) ? 'checked' : '' }}
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="fuzzy_matching" class="ml-2 text-sm text-gray-700">
                                    Enable fuzzy matching
                                </label>
                            </div>

                            <!-- Similarity Threshold -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Similarity Threshold (%)</label>
                                <input type="number" 
                                       name="similarity_threshold" 
                                       value="{{ $field['matching']['similarity_threshold'] ?? 70 }}"
                                       min="0"
                                       max="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            </div>

                            <!-- Case Sensitive -->
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       name="case_sensitive" 
                                       id="case_sensitive"
                                       {{ ($field['matching']['case_sensitive'] ?? false) ? 'checked' : '' }}
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="case_sensitive" class="ml-2 text-sm text-gray-700">
                                    Case sensitive matching
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200 mt-6">
                <div>
                    @if(!$is_new && !unified_field_definitions::is_custom_field($field_name))
                        <button type="button"
                                class="px-4 py-2 text-sm font-medium text-orange-700 bg-orange-100 border border-orange-300 rounded-md hover:bg-orange-200"
                                hx-post="{{ APP_ROOT }}/api/unified_field_definitions/reset"
                                hx-vals='{"field_name": "{{ $field_name }}"}'
                                hx-target="#modal_body"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to reset this field to its default configuration? This will remove any custom changes.">
                            Reset to Default
                        </button>
                    @endif
                </div>
                
                <div class="flex space-x-3">
                    <button type="button"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                            @click="modal = false">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700">
                        {{ $is_new ? 'Create Field' : 'Save Changes' }}
                    </button>
                </div>
            </div>
        </form>

<!-- Form processing handled by HTMX, modal interactions handled by Alpine.js -->
