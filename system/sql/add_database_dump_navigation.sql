-- Add Database Dump Manager to Navigation System
-- Run this SQL script to add the database dump functionality to the system navigation

-- Check if 'system' parent exists, if not create it
INSERT IGNORE INTO `autobooks_navigation` (
    `parent_path`,
    `route_key`,
    `name`,
    `icon`,
    `file_path`,
    `required_roles`,
    `sort_order`,
    `show_navbar`,
    `can_delete`,
    `is_system`
) VALUES (
    'root',
    'system',
    'System',
    'cog',
    '',
    '["admin", "dev"]',
    100,
    1,
    0,
    1
);

-- Add the Database Dump Manager entry
INSERT INTO `autobooks_navigation` (
    `parent_path`,
    `route_key`,
    `name`,
    `icon`,
    `file_path`,
    `required_roles`,
    `sort_order`,
    `show_navbar`,
    `can_delete`,
    `is_system`
) VALUES (
    'system',
    'database_dump',
    'Database Dump Manager',
    'database',
    'system',
    '["admin", "dev"]',
    10,
    1,
    0,
    1
) ON DUPLICATE KEY UPDATE 
    `name` = VALUES(`name`),
    `icon` = VALUES(`icon`),
    `required_roles` = VALUES(`required_roles`),
    `file_path` = VALUES(`file_path`),
    `is_system` = VALUES(`is_system`);

-- Verify the entries were added
SELECT 'Navigation entries added successfully!' as status;
SELECT * FROM autobooks_navigation WHERE route_key IN ('system', 'database_dump') ORDER BY parent_path, sort_order;
