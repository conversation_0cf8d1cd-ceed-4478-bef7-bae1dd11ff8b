-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 15, 2025 at 07:45 AM
-- Table: affiliate_banners
-- Records: 1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `affiliate_banners`
--

DROP TABLE IF EXISTS `affiliate_banners`;
CREATE TABLE `affiliate_banners` (
  `affiliate_banners_id` int(11) NOT NULL AUTO_INCREMENT,
  `affiliate_banners_title` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `affiliate_products_id` int(11) NOT NULL DEFAULT 0,
  `affiliate_banners_image` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `affiliate_banners_group` varchar(10) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `affiliate_banners_html_text` mediumtext COLLATE utf8_unicode_ci DEFAULT NULL,
  `affiliate_expires_impressions` int(7) DEFAULT 0,
  `affiliate_expires_date` datetime DEFAULT NULL,
  `affiliate_date_scheduled` datetime DEFAULT NULL,
  `affiliate_date_added` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `affiliate_date_status_change` datetime DEFAULT NULL,
  `affiliate_status` int(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`affiliate_banners_id`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Dumping data for table `affiliate_banners`
--

INSERT INTO `affiliate_banners` (`affiliate_banners_id`, `affiliate_banners_title`, `affiliate_products_id`, `affiliate_banners_image`, `affiliate_banners_group`, `affiliate_banners_html_text`, `affiliate_expires_impressions`, `affiliate_expires_date`, `affiliate_date_scheduled`, `affiliate_date_added`, `affiliate_date_status_change`, `affiliate_status`) VALUES
('2', 'Register_DesignJet', '0', 'DJ_Registration.gif', '', NULL, '0', NULL, NULL, '2004-02-08 15:25:51', NULL, '1');

COMMIT;
