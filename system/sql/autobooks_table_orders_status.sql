-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 15, 2025 at 07:45 AM
-- Table: orders_status
-- Records: 8

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `orders_status`
--

DROP TABLE IF EXISTS `orders_status`;
CREATE TABLE `orders_status` (
  `orders_status_id` int(11) NOT NULL DEFAULT 0,
  `language_id` int(11) NOT NULL DEFAULT 1,
  `orders_status_name` varchar(32) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `public_flag` int(11) DEFAULT 1,
  `downloads_flag` int(11) DEFAULT 0,
  PRIMARY KEY (`orders_status_id`,`language_id`),
  KEY `idx_orders_status_name` (`orders_status_name`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Dumping data for table `orders_status`
--

INSERT INTO `orders_status` (`orders_status_id`, `language_id`, `orders_status_name`, `public_flag`, `downloads_flag`) VALUES
('1', '1', 'Pending', '1', '0'),
('2', '1', 'Processing', '1', '0'),
('3', '1', 'Delivered', '1', '0'),
('4', '1', 'Cancelled', '1', '0'),
('5', '1', 'Authorize.net [Transactions]', '0', '0'),
('6', '1', 'Stripe [Transactions]', '0', '0'),
('7', '1', 'Braintree [Transactions]', '0', '0'),
('0', '1', 'Failed, Please Try again', '1', '0');

COMMIT;
