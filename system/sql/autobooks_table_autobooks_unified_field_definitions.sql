-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 17, 2025 at 12:25 AM
-- Table: autobooks_unified_field_definitions
-- Records: 13

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autobooks_unified_field_definitions`
--

DROP TABLE IF EXISTS `autobooks_unified_field_definitions`;
CREATE TABLE `autobooks_unified_field_definitions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `field_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `field_definition` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `field_name` (`field_name`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `autobooks_unified_field_definitions`
--

INSERT INTO `autobooks_unified_field_definitions` (`id`, `field_name`, `field_definition`, `is_active`, `created_at`, `updated_at`) VALUES
('1', 'serial_number', '{\"label\":\"Serial Number\",\"type\":\"string\",\"category\":\"identification\",\"description\":\"Product or license serial number\",\"patterns\":[\"serial\\r\",\"serial_number\\r\",\"serial_no\\r\",\"product_serial\\r\",\"license_serial\"],\"normalized_fields\":[\"serial_number\\r\",\"product_serial\\r\",\"license_serial\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 09:28:01', '2025-08-16 17:32:24'),
('2', 'start_date', '{\"label\":\"Start Date\",\"type\":\"date\",\"category\":\"dates\",\"description\":\"Subscription or agreement start date\",\"patterns\":[\"start_date\\r\",\"date_start\\r\",\"purchase_date\\r\",\"agreement_start_date\\r\",\"subscription_start_date\\r\",\"activation_date\\r\",\"begin_date\\r\",\"subs_startDate\"],\"normalized_fields\":[\"start_date\\r\",\"subs_startDate\\r\",\"agreement_start_date\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 09:36:30', '2025-08-16 09:36:30'),
('3', 'contact_name', '{\"label\":\"Contact Name\",\"type\":\"string\",\"category\":\"contact\",\"description\":\"Primary contact person name\",\"patterns\":[\"contact\\r\",\"contact_name\\r\",\"name\\r\",\"customer_contact\\r\",\"end_customer_contact_name\\r\",\"subscription_contact_name\\r\",\"admin_name\\r\",\"primary_contact\\r\",\"first_name\\r\",\"last_name\"],\"normalized_fields\":[\"contact_name\\r\",\"end_customer_contact_name\\r\",\"subscription_contact_name\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":3,\"case_sensitive\":false,\"fuzzy_matching\":true,\"similarity_threshold\":80,\"exact_match_only\":false}}', '1', '2025-08-16 09:48:44', '2025-08-16 09:48:44'),
('4', 'quantity', '{\"label\":\"Quantity\",\"type\":\"number\",\"category\":\"product\",\"description\":\"Number of licenses or seats\",\"patterns\":[\"quantity\\r\",\"qty\\r\",\"licenses\\r\",\"seats\\r\",\"subscription_quantity\\r\",\"users\\r\",\"count\\r\",\"license_count\\r\",\"seat_count\\r\",\"subs_quantity\"],\"normalized_fields\":[\"quantity\\r\",\"subs_quantity\\r\",\"subscription_quantity\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 10:01:55', '2025-08-16 10:01:55'),
('5', 'end_date', '{\"label\":\"End Date\",\"type\":\"date\",\"category\":\"dates\",\"description\":\"Subscription or agreement end\\/expiry date\",\"patterns\":[\"end_date\\r\",\"expiry_date\\r\",\"renewal_date\\r\",\"date_end\\r\",\"agreement_end_date\\r\",\"subscription_end_date\\r\",\"expiration_date\\r\",\"due_date\\r\",\"subs_endDate\"],\"normalized_fields\":[\"end_date\\r\",\"subs_endDate\\r\",\"agreement_end_date\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 11:47:54', '2025-08-16 11:47:54'),
('6', 'status', '{\"label\":\"Status\",\"type\":\"string\",\"category\":\"status\",\"description\":\"Subscription or license status\",\"patterns\":[\"status\\r\",\"subscription_status\\r\",\"agreement_status\\r\",\"active\\r\",\"license_status\\r\",\"account_status\\r\",\"subscription_state\\r\",\"license_state\\r\",\"subs_status\"],\"normalized_fields\":[\"status\\r\",\"subs_status\\r\",\"subscription_status\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 11:48:06', '2025-08-16 17:23:43'),
('7', 'address', '{\"label\":\"Address\",\"type\":\"string\",\"category\":\"address\",\"description\":\"Street address or business address\",\"patterns\":[\"address\\r\",\"address_1\\r\",\"street\\r\",\"end_customer_address_1\\r\",\"business_address\\r\",\"shipping_address\\r\",\"billing_address\\r\",\"order_shipping_address\"],\"normalized_fields\":[\"address\\r\",\"end_customer_address_1\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":true,\"similarity_threshold\":70,\"exact_match_only\":false}}', '1', '2025-08-16 11:48:29', '2025-08-16 11:48:29'),
('8', 'city', '{\"label\":\"City\",\"type\":\"string\",\"category\":\"address\",\"description\":\"City or town name\",\"patterns\":[\"city\\r\",\"end_customer_city\\r\",\"town\\r\",\"shipping_city\\r\",\"billing_city\\r\",\"order_shipping_city\"],\"normalized_fields\":[\"city\\r\",\"end_customer_city\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":true,\"similarity_threshold\":70,\"exact_match_only\":false}}', '1', '2025-08-16 12:00:19', '2025-08-16 12:00:19'),
('9', 'country', '{\"label\":\"Country\",\"type\":\"string\",\"category\":\"address\",\"description\":\"Country name\",\"patterns\":[\"country\\r\",\"end_customer_country\\r\",\"nation\\r\",\"shipping_country\\r\",\"billing_country\\r\",\"order_shipping_country\"],\"normalized_fields\":[\"country\\r\",\"end_customer_country\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 12:00:40', '2025-08-16 12:00:40'),
('10', 'state', '{\"label\":\"State\\/Province\",\"type\":\"string\",\"category\":\"address\",\"description\":\"State, province, or region\",\"patterns\":[\"state\\r\",\"province\\r\",\"region\\r\",\"end_customer_state\\r\",\"shipping_state\\r\",\"billing_state\\r\",\"order_shipping_state_province\\r\",\"state_province\"],\"normalized_fields\":[\"state\\r\",\"end_customer_state\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 17:25:10', '2025-08-16 17:25:10'),
('11', 'postal_code', '{\"label\":\"Postal Code\",\"type\":\"string\",\"category\":\"address\",\"description\":\"ZIP code or postal code\",\"patterns\":[\"zip\\r\",\"postal_code\\r\",\"zip_code\\r\",\"postcode\\r\",\"end_customer_zip_code\\r\",\"shipping_zip\\r\",\"billing_zip\"],\"normalized_fields\":[\"postal_code\\r\",\"end_customer_zip_code\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 17:29:35', '2025-08-16 17:29:35'),
('12', 'currency', '{\"label\":\"Currency\",\"type\":\"string\",\"category\":\"financial\",\"description\":\"Currency code (USD, EUR, etc.)\",\"patterns\":[\"currency\\r\",\"currency_code\\r\",\"price_currency\"],\"normalized_fields\":[\"currency\\r\",\"currency_code\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 17:32:56', '2025-08-16 17:32:56'),
('13', 'price', '{\"label\":\"Price\",\"type\":\"currency\",\"category\":\"financial\",\"description\":\"Subscription or license price\",\"patterns\":[\"price\\r\",\"cost\\r\",\"amount\\r\",\"subscription_price\\r\",\"license_cost\\r\",\"total_price\\r\",\"unit_price\\r\",\"annual_price\"],\"normalized_fields\":[\"price\\r\",\"subscription_price\\r\",\"total_price\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 17:36:33', '2025-08-16 17:36:33');

COMMIT;
