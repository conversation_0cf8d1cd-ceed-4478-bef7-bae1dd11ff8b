<?php


class startup_sequence
{
    public static function start($path, $schema): void
    {
        IF (!defined('DS')) define('DS', DIRECTORY_SEPARATOR);
        IF (!defined('API_RUN')) define('API_RUN', false);
        IF (!defined('DEBUG_MODE')) define('DEBUG_MODE', false);
        //echo'<!-- starting startup sequence class -->';
        if (!isset($path) || !is_array($path)) $path = [];
        if (!isset($path['fs_app_root'])) $path['fs_app_root'] = str_replace("system", "", __DIR__);
        // echo'<!-- starting paths -->';
        require_once("{$path['fs_system']}/paths.php");

        $path = build_paths($path, $schema);
        build_constants($path);
        $path = build_routes($path);
        // echo'<!-- starting functions -->';
        require_once("{$path['fs_system']}/functions/functions.php");
// Register custom error handlers to capture PHP errors
//tcs_register_error_handlers();


     //   print_rr('initializing autoloader');
        require_once("{$path['fs_system']}" . DIRECTORY_SEPARATOR . "autoloader.php");

//print_rr('starting');

//legacy components
        // echo"<!-- loading legacy components -->";
        require_once("{$path['fs_system']}/functions/database.php");
        require_once("{$path['fs_system']}/functions/components.fn.php");


// Database connection details
        $tcs_database = str_replace('.', '', DOMAIN);
        $db_server = 'localhost';
        $db_username = $tcs_database;
        $db_password = 'S96#1kvYuCGE';
        $db_database = $tcs_database;

        // echo"'$tcs_database'";
        if ($tcs_database == 'localhost') {
            $tcs_database = 'wwwcadservicescouk';
            $is_local = true;
            $db_server = 'localhost';
            $db_username = $tcs_database;
            $db_password = 'S96#1kvYuCGE';
            $db_database = $tcs_database;
            // echo"LOCAL";
        }

//      if ($is_local) {
//          // echo"<!--ss db LOCAL DEV MODE: Using database '$db_database' on '$db_server' user: $db_username and password: " . preg_replace('/[a-zA-Z0-9]/', '*',  $db_password) . " -->\n";
//      }
        $db = tep_db_connect($db_server, $db_username, $db_password, $db_database) or die('Unable to connect to database server!');
        // echo"<!-- db inittted -->";
// Load core classes and dependencies
        require_once(DOC_ROOT . DS . 'vendor' . DS . 'autoload.php');

// Load custom autoloader


// Load essential components that might not be autoloaded
        if (file_exists($path['fs_app_root'] . "/resources/components/icons.php")) {
            require_once($path['fs_app_root'] . "/resources/components/icons.php");
        }

// Initialize modal tabs functionality
        if (file_exists($path['fs_app_root'] . "/system/init/modal_tabs_init.php")) {
            require_once($path['fs_app_root'] . "/system/init/modal_tabs_init.php");
        }

// Initialize authentication and role-based access control
        session_start();

// Skip authentication for login page and public routes
        $current_script = basename($_SERVER['SCRIPT_NAME']);
        $public_routes = ['login', 'create_admin.php', 'reset-password'];
        $user = null;
        if (!in_array(CURRENT_PAGE, $public_routes)) {
            // Check if user is authenticated
            $user = \system\users::checkAuth();
            if (!$user) header('Location: ' . APP_ROOT . DIRECTORY_SEPARATOR . 'login');

            // Load route permissions
            $permissions = include($path['fs_app_root'] . DIRECTORY_SEPARATOR . 'system/route_permissions.php');

            // Get current route from URL
            $current_route = trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');
            if (empty($current_route)) $current_route = 'dashboard';

            // Check if route requires specific role
            $required_role = $permissions['routes'][$current_route] ?? $permissions['default'];

            // Verify user has required role
            if ($required_role) \system\users::requireRole($required_role);

        }
        print_rr($path);
        print_rr($_SESSION);
// After successful authentication, load user preferences
        if ($user) {
            $preferences = tep_db_query("SELECT preferences FROM autobooks_users WHERE id = :user_id", null, [':user_id' => $user['id']]);
            print_rr('am I in debug mode?');
            if ($row = tep_db_fetch_array($preferences)) {
                $prefs = json_decode($row['preferences'], true) ?? [];
                $_SESSION['debug_mode'] = isset($prefs['debug_mode']) ? (bool)$prefs['debug_mode'] : false;
                print_rr($row, '$row');
                print_rr($row['preferences'], 'preferences');
                print_rr($_SESSION['debug_mode'] ? 'on' : 'off', 'debug mode is: ');
            }
            define('POOFED', true);
            // Flush any delayed print_rr outputs now that POOFED is defined
            if (function_exists('flush_delayed_print_rr')) {
                flush_delayed_print_rr();
            }
            define('USER_ROLE', $user['role']);
            print_rr($_SESSION);
        } else {
            print_rr('no user');
        }

    }
}