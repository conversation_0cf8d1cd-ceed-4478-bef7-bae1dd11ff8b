[enhanced_data_search_debug] [2025-08-11 14:53:01] [enhanced_data.api.php:187] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 14:53:01] [enhanced_data.api.php:187] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 14:53:24] [enhanced_data.api.php:187] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 14:53:24] [enhanced_data.api.php:187] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 14:55:54] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 14:55:54] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 14:55:54] [enhanced_data.api.php:158] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 14:55:54] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 14:55:54] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:04:38] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:04:38] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:04:38] [enhanced_data.api.php:158] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:04:38] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:04:38] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:04:52] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:04:52] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:04:52] [enhanced_data.api.php:158] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:04:52] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:04:52] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:08:48] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => bsba\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => bsba\n        )\n\n    [table_name] => autobooks_import_sketchup_data\n)\n
[enhanced_data_search_debug] [2025-08-11 15:08:48] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => bsba\n)\n
[enhanced_data_search_debug] [2025-08-11 15:08:48] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_sketchup_data\n            [just_body] => 1\n            [search] => bsba\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:08:48] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:08:48] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:08:48] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:08:48] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:16:05] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => bsba\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => bsba\n        )\n\n    [table_name] => autobooks_import_sketchup_data\n)\n
[enhanced_data_search_debug] [2025-08-11 15:16:05] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => bsba\n)\n
[enhanced_data_search_debug] [2025-08-11 15:16:05] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_sketchup_data\n            [just_body] => 1\n            [search] => bsba\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:16:05] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:16:05] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:16:05] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:16:05] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:43] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => \n        )\n\n    [table_name] => autobooks_import_sketchup_data\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:43] [enhanced_data_callbacks.php:42]  Array\n(\n    [action] => no_search_terms_found\n    [criteria] => Array\n        (\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => \n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:43] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_sketchup_data\n            [just_body] => 1\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:43] [enhanced_data_callbacks.php:83]  Array\n(\n    [action] => enhanced_data_api_result\n    [result_type] => string\n    [result_length] => 307677\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:44] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => b\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => b\n        )\n\n    [table_name] => autobooks_import_sketchup_data\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:44] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => b\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:44] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_sketchup_data\n            [just_body] => 1\n            [search] => b\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:44] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:44] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => b\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:44] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:44] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => b\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:46] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => bsba\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => bsba\n        )\n\n    [table_name] => autobooks_import_sketchup_data\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:46] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => bsba\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:46] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_sketchup_data\n            [just_body] => 1\n            [search] => bsba\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:46] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:46] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:46] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 15:25:46] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:49] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => BS\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => BS\n        )\n\n    [table_name] => autobooks_import_sketchup_data\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:49] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => BS\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:49] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_sketchup_data\n            [just_body] => 1\n            [search] => BS\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:49] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:49] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => BS\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:49] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:49] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => BS\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:50] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => BSB\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => BSB\n        )\n\n    [table_name] => autobooks_import_sketchup_data\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:50] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => BSB\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:50] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_sketchup_data\n            [just_body] => 1\n            [search] => BSB\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:50] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:50] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => BSB\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:50] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:50] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => BSB\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:51] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => BSBA\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => BSBA\n        )\n\n    [table_name] => autobooks_import_sketchup_data\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:51] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => BSBA\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:51] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_sketchup_data\n            [just_body] => 1\n            [search] => BSBA\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:51] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:51] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => BSBA\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:51] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 19:37:51] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => BSBA\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:49] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => BSB\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => BSB\n        )\n\n    [table_name] => autobooks_import_sketchup_data\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:49] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => BSB\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:49] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_sketchup_data\n            [just_body] => 1\n            [search] => BSB\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:49] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:49] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => BSB\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:49] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:49] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => BSB\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:50] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => BSBA\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_sketchup_data\n            [search_terms] => BSBA\n        )\n\n    [table_name] => autobooks_import_sketchup_data\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:50] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => BSBA\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:50] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_sketchup_data\n            [just_body] => 1\n            [search] => BSBA\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:50] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:50] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => BSBA\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:50] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_sketchup_data\n    [total_columns] => 62\n    [searchable_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => sold_to_name\n            [2] => sold_to_number\n            [3] => vendor_name\n            [4] => reseller_number\n            [5] => reseller_vendor_id\n            [6] => end_customer_vendor_id\n            [7] => end_customer_name\n            [8] => end_customer_address_1\n            [9] => end_customer_address_2\n            [10] => end_customer_address_3\n            [11] => end_customer_city\n            [12] => end_customer_state\n            [13] => end_customer_zip_code\n            [14] => end_customer_country\n            [15] => end_customer_account_type\n            [16] => end_customer_contact_name\n            [17] => end_customer_contact_email\n            [18] => end_customer_contact_phone\n            [19] => end_customer_industry_segment\n            [20] => agreement_program_name\n            [21] => agreement_number\n            [22] => agreement_start_date\n            [23] => agreement_end_date\n            [24] => agreement_terms\n            [25] => agreement_type\n            [26] => agreement_status\n            [27] => agreement_support_level\n            [28] => agreement_days_due\n            [29] => agreement_autorenew\n            [30] => product_name\n            [31] => product_family\n            [32] => product_market_segment\n            [33] => product_release\n            [34] => product_type\n            [35] => product_deployment\n            [36] => product_sku\n            [37] => product_sku_description\n            [38] => product_part\n            [39] => product_list_price\n            [40] => product_list_price_currency\n            [41] => subscription_id\n            [42] => subscription_serial_number\n            [43] => subscription_status\n            [44] => subscription_quantity\n            [45] => subscription_start_date\n            [46] => subscription_end_date\n            [47] => subscription_contact_name\n            [48] => subscription_contact_email\n            [49] => subscription_level\n            [50] => subscription_days_due\n            [51] => quotation_id\n            [52] => quotation_type\n            [53] => quotation_vendor_id\n            [54] => quotation_deal_registration_number\n            [55] => quotation_status\n            [56] => quotation_resellerpo_previous\n            [57] => quotation_due_date\n            [58] => flaer_phase\n            [59] => updated\n            [60] => created_at\n            [61] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [sold_to_name] => varchar(255)\n            [sold_to_number] => int(11)\n            [vendor_name] => varchar(255)\n            [reseller_number] => int(11)\n            [reseller_vendor_id] => varchar(255)\n            [end_customer_vendor_id] => int(11)\n            [end_customer_name] => varchar(255)\n            [end_customer_address_1] => varchar(255)\n            [end_customer_address_2] => varchar(255)\n            [end_customer_address_3] => varchar(255)\n            [end_customer_city] => varchar(255)\n            [end_customer_state] => varchar(255)\n            [end_customer_zip_code] => varchar(255)\n            [end_customer_country] => varchar(255)\n            [end_customer_account_type] => varchar(255)\n            [end_customer_contact_name] => varchar(255)\n            [end_customer_contact_email] => varchar(255)\n            [end_customer_contact_phone] => varchar(255)\n            [end_customer_industry_segment] => varchar(255)\n            [agreement_program_name] => varchar(255)\n            [agreement_number] => int(11)\n            [agreement_start_date] => varchar(255)\n            [agreement_end_date] => varchar(255)\n            [agreement_terms] => varchar(255)\n            [agreement_type] => varchar(255)\n            [agreement_status] => varchar(255)\n            [agreement_support_level] => varchar(255)\n            [agreement_days_due] => int(11)\n            [agreement_autorenew] => int(11)\n            [product_name] => varchar(255)\n            [product_family] => varchar(255)\n            [product_market_segment] => varchar(255)\n            [product_release] => varchar(255)\n            [product_type] => varchar(255)\n            [product_deployment] => varchar(255)\n            [product_sku] => varchar(255)\n            [product_sku_description] => varchar(255)\n            [product_part] => varchar(255)\n            [product_list_price] => int(11)\n            [product_list_price_currency] => varchar(255)\n            [subscription_id] => varchar(255)\n            [subscription_serial_number] => varchar(255)\n            [subscription_status] => varchar(255)\n            [subscription_quantity] => int(11)\n            [subscription_start_date] => varchar(255)\n            [subscription_end_date] => varchar(255)\n            [subscription_contact_name] => varchar(255)\n            [subscription_contact_email] => varchar(255)\n            [subscription_level] => varchar(255)\n            [subscription_days_due] => int(11)\n            [quotation_id] => varchar(255)\n            [quotation_type] => varchar(255)\n            [quotation_vendor_id] => int(11)\n            [quotation_deal_registration_number] => varchar(255)\n            [quotation_status] => varchar(255)\n            [quotation_resellerpo_previous] => varchar(255)\n            [quotation_due_date] => varchar(255)\n            [flaer_phase] => varchar(255)\n            [updated] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:16:50] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => BSBA\n    [table_name] => autobooks_import_sketchup_data\n    [search_columns] => Array\n        (\n            [0] => sold_to_name\n            [1] => vendor_name\n            [2] => reseller_vendor_id\n            [3] => end_customer_name\n            [4] => end_customer_address_1\n            [5] => end_customer_address_2\n            [6] => end_customer_address_3\n            [7] => end_customer_city\n            [8] => end_customer_state\n            [9] => end_customer_zip_code\n            [10] => end_customer_country\n            [11] => end_customer_account_type\n            [12] => end_customer_contact_name\n            [13] => end_customer_contact_email\n            [14] => end_customer_contact_phone\n            [15] => end_customer_industry_segment\n            [16] => agreement_program_name\n            [17] => agreement_start_date\n            [18] => agreement_end_date\n            [19] => agreement_terms\n            [20] => agreement_type\n            [21] => agreement_status\n            [22] => agreement_support_level\n            [23] => product_name\n            [24] => product_family\n            [25] => product_market_segment\n            [26] => product_release\n            [27] => product_type\n            [28] => product_deployment\n            [29] => product_sku\n            [30] => product_sku_description\n            [31] => product_part\n            [32] => product_list_price_currency\n            [33] => subscription_id\n            [34] => subscription_serial_number\n            [35] => subscription_status\n            [36] => subscription_start_date\n            [37] => subscription_end_date\n            [38] => subscription_contact_name\n            [39] => subscription_contact_email\n            [40] => subscription_level\n            [41] => quotation_id\n            [42] => quotation_type\n            [43] => quotation_deal_registration_number\n            [44] => quotation_status\n            [45] => quotation_resellerpo_previous\n            [46] => quotation_due_date\n            [47] => flaer_phase\n            [48] => updated\n        )\n\n    [search_columns_count] => 49\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:12] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => b\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_bluebeam_data\n            [search_terms] => b\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:12] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => b\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:12] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_bluebeam_data\n            [just_body] => 1\n            [search] => b\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:12] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:12] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => b\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:12] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:12] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => b\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:13] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => bs\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_bluebeam_data\n            [search_terms] => bs\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:13] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => bs\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:13] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_bluebeam_data\n            [just_body] => 1\n            [search] => bs\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:13] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:13] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bs\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:13] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:13] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bs\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:15] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => bsbas\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_bluebeam_data\n            [search_terms] => bsbas\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:15] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => bsbas\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:15] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_bluebeam_data\n            [just_body] => 1\n            [search] => bsbas\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:15] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:15] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsbas\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:15] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:15] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsbas\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:16] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => bsba\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_bluebeam_data\n            [search_terms] => bsba\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:16] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => bsba\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:16] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_bluebeam_data\n            [just_body] => 1\n            [search] => bsba\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:16] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:16] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:16] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 20:26:16] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 21:59:41] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => bsba\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_bluebeam_data\n            [search_terms] => bsba\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n)\n
[enhanced_data_search_debug] [2025-08-11 21:59:41] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => bsba\n)\n
[enhanced_data_search_debug] [2025-08-11 21:59:41] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_bluebeam_data\n            [just_body] => 1\n            [search] => bsba\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 21:59:41] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 21:59:41] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 21:59:41] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 21:59:41] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 22:14:28] [enhanced_data_callbacks.php:15]  Array\n(\n    [action] => enhanced_data_table_filter_called\n    [criteria] => Array\n        (\n            [search] => bsba\n        )\n\n    [original_params] => Array\n        (\n            [callback] => enhanced_data_table_filter\n            [table_name] => autobooks_import_bluebeam_data\n            [search_terms] => bsba\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n)\n
[enhanced_data_search_debug] [2025-08-11 22:14:28] [enhanced_data_callbacks.php:31]  Array\n(\n    [action] => search_from_original_params\n    [search_terms] => bsba\n)\n
[enhanced_data_search_debug] [2025-08-11 22:14:28] [enhanced_data_callbacks.php:74]  Array\n(\n    [action] => calling_enhanced_data_api\n    [final_params] => Array\n        (\n            [table_name] => autobooks_import_bluebeam_data\n            [just_body] => 1\n            [search] => bsba\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 22:14:28] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 22:14:28] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
[enhanced_data_search_debug] [2025-08-11 22:14:28] [enhanced_data.api.php:220] Array\n(\n    [action] => get_searchable_columns\n    [table_name] => autobooks_import_bluebeam_data\n    [total_columns] => 15\n    [searchable_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [all_columns] => Array\n        (\n            [0] => id\n            [1] => serial_number\n            [2] => name\n            [3] => contract\n            [4] => product_name\n            [5] => quantity\n            [6] => end_date\n            [7] => account_primary_reseller_name\n            [8] => order_po_number\n            [9] => order_shipping_address\n            [10] => order_shipping_city\n            [11] => order_shipping_state_province\n            [12] => order_shipping_country\n            [13] => created_at\n            [14] => updated_at\n        )\n\n    [column_types] => Array\n        (\n            [id] => int(10) unsigned\n            [serial_number] => varchar(255)\n            [name] => varchar(255)\n            [contract] => varchar(255)\n            [product_name] => varchar(255)\n            [quantity] => varchar(255)\n            [end_date] => varchar(255)\n            [account_primary_reseller_name] => varchar(255)\n            [order_po_number] => varchar(255)\n            [order_shipping_address] => varchar(255)\n            [order_shipping_city] => varchar(255)\n            [order_shipping_state_province] => varchar(255)\n            [order_shipping_country] => varchar(255)\n            [created_at] => timestamp\n            [updated_at] => timestamp\n        )\n\n)\n
[enhanced_data_search_debug] [2025-08-11 22:14:28] [enhanced_data.api.php:143] Array\n(\n    [action] => build_search_criteria\n    [search_term] => bsba\n    [table_name] => autobooks_import_bluebeam_data\n    [search_columns] => Array\n        (\n            [0] => serial_number\n            [1] => name\n            [2] => contract\n            [3] => product_name\n            [4] => quantity\n            [5] => end_date\n            [6] => account_primary_reseller_name\n            [7] => order_po_number\n            [8] => order_shipping_address\n            [9] => order_shipping_city\n            [10] => order_shipping_state_province\n            [11] => order_shipping_country\n        )\n\n    [search_columns_count] => 12\n)\n
