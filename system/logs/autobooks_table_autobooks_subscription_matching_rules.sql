-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 15, 2025 at 07:45 AM
-- Table: autobooks_subscription_matching_rules
-- Records: 5

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autobooks_subscription_matching_rules`
--

DROP TABLE IF EXISTS `autobooks_subscription_matching_rules`;
CREATE TABLE `autobooks_subscription_matching_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 99,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `configuration` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `rule_name` (`rule_name`),
  KEY `priority` (`priority`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `autobooks_subscription_matching_rules`
--

INSERT INTO `autobooks_subscription_matching_rules` (`id`, `rule_name`, `priority`, `is_active`, `configuration`, `created_at`, `updated_at`) VALUES
('1', 'email_matching', '1', '1', '{\"enabled\":true,\"priority\":1,\"confidence_score\":100,\"field_patterns\":[\"endcust_primary_admin_email\",\"end_customer_contact_email\",\"subscription_contact_email\",\"email_address\",\"email\",\"contact_email\",\"admin_email\",\"customer_email\"],\"case_sensitive\":false,\"exact_match_only\":true}', '2025-08-07 15:03:57', '2025-08-07 15:03:57'),
('2', 'company_name_matching', '2', '1', '{\"enabled\":true,\"priority\":2,\"confidence_threshold\":70,\"field_patterns\":[\"endcust_name\",\"end_customer_name\",\"company_name\",\"customer_name\",\"organization_name\",\"business_name\",\"sold_to_name\",\"vendor_name\"],\"fuzzy_matching\":true,\"similarity_threshold\":70,\"case_sensitive\":false,\"preprocessing\":{\"remove_common_words\":[\"ltd\",\"limited\",\"inc\",\"corp\",\"corporation\",\"llc\",\"plc\"],\"normalize_spaces\":true,\"remove_punctuation\":true}}', '2025-08-07 15:03:57', '2025-08-07 15:03:57'),
('3', 'subscription_reference_matching', '0', '1', '{\"enabled\":true,\"priority\":0,\"confidence_score\":100,\"field_patterns\":[\"subscription_reference\",\"subscription_id\",\"agreement_number\",\"contract_number\",\"license_key\",\"serial_number\",\"reference_number\"],\"case_sensitive\":false,\"exact_match_only\":true}', '2025-08-07 15:03:57', '2025-08-07 15:03:57'),
('4', 'contact_name_matching', '3', '0', '{\"enabled\":false,\"priority\":3,\"confidence_threshold\":80,\"field_patterns\":[\"contact_name\",\"end_customer_contact_name\",\"subscription_contact_name\",\"admin_name\",\"primary_contact\"],\"fuzzy_matching\":true,\"similarity_threshold\":80,\"case_sensitive\":false}', '2025-08-07 15:03:57', '2025-08-07 15:03:57'),
('5', 'phone_matching', '4', '0', '{\"enabled\":false,\"priority\":4,\"confidence_score\":90,\"field_patterns\":[\"phone\",\"phone_number\",\"contact_phone\",\"end_customer_contact_phone\",\"business_phone\"],\"normalize_phone\":true,\"exact_match_only\":true}', '2025-08-07 15:03:57', '2025-08-07 15:03:57');

COMMIT;
