[database] [2025-08-11 09:03:50] [database.class.php:264] First result: [{"file_path":"system","is_system":true,"route_key":"system"}]\nquery: SELECT file_path, is_system, route_key FROM autobooks_navigation WHERE `route_key` = :where_route_key_0 AND `parent_path` = :where_parent_path_1 LIMIT 1
[database] [2025-08-11 09:15:51] [database.class.php:264] First result: [{"file_path":"system","is_system":true,"route_key":"system"}]\nquery: SELECT file_path, is_system, route_key FROM autobooks_navigation WHERE `route_key` = :where_route_key_0 AND `parent_path` = :where_parent_path_1 LIMIT 1
[database] [2025-08-11 09:16:35] [database.class.php:264] First result: [{"file_path":"system","is_system":true,"route_key":"system"}]\nquery: SELECT file_path, is_system, route_key FROM autobooks_navigation WHERE `route_key` = :where_route_key_0 AND `parent_path` = :where_parent_path_1 LIMIT 1
[database] [2025-08-11 09:38:14] [database.class.php:264] First result: [{"file_path":"system","is_system":true,"route_key":"system"}]\nquery: SELECT file_path, is_system, route_key FROM autobooks_navigation WHERE `route_key` = :where_route_key_0 AND `parent_path` = :where_parent_path_1 LIMIT 1
[database] [2025-08-11 09:48:48] [database.class.php:264] First result: [{"file_path":"system","is_system":true,"route_key":"system"}]\nquery: SELECT file_path, is_system, route_key FROM autobooks_navigation WHERE `route_key` = :where_route_key_0 AND `parent_path` = :where_parent_path_1 LIMIT 1
