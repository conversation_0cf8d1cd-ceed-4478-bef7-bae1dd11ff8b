[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-14 14:10:38
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d5e728bf-256b-464f-8471-eb34b687ef2c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59766978865168\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-16\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-14T13:35:33.000+0000\n        )\n\n    [publishedAt] => 2025-08-14T14:10:36.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-14 14:10:38
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d5e728bf-256b-464f-8471-eb34b687ef2c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59766978865168\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-16\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-14T13:35:33.000+0000\n        )\n\n    [publishedAt] => 2025-08-14T14:10:36.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59766978865168', status = 'Active', quantity = 1, endDate = '2026-08-16', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59766978865168', status = 'Active', quantity = 1, endDate = '2026-08-16', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-14 14:10:38] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59766978865168', status = 'Active', quantity = 1, endDate = '2026-08-16', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59766978865168', status = 'Active', quantity = 1, endDate = '2026-08-16', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-14 19:37:46
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bbe0f2cd-8b09-4322-9ac6-c8bc7d8656ee\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59766978865168\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-14T19:17:38.000+0000\n        )\n\n    [publishedAt] => 2025-08-14T19:37:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59766978865168', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59766978865168', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-14 19:37:46
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bbe0f2cd-8b09-4322-9ac6-c8bc7d8656ee\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59766978865168\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-14T19:17:38.000+0000\n        )\n\n    [publishedAt] => 2025-08-14T19:37:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-14 19:37:46] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59766978865168', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59766978865168', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 00:06:14
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1307e69e-d218-4d72-9f16-143586187c54\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75368953253895\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-14T23:41:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T00:06:11.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75368953253895', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '75368953253895', status = 'Suspended';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 00:06:14
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1307e69e-d218-4d72-9f16-143586187c54\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75368953253895\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-14T23:41:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T00:06:11.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-15 00:06:14] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75368953253895', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '75368953253895', status = 'Suspended';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 00:10:45
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bfcc0901-3aae-48b8-9da6-32eca330ade7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75127504067843\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-14T23:40:40.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T00:10:43.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75127504067843', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '75127504067843', status = 'Suspended';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 00:10:45
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bfcc0901-3aae-48b8-9da6-32eca330ade7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75127504067843\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-14T23:40:40.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T00:10:43.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-15 00:10:45] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75127504067843', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '75127504067843', status = 'Suspended';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 06:44:32
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 04b5c5aa-77f1-4fc1-ad0a-a3dc940eacde\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62521806254194\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-15T06:29:27.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T06:44:30.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62521806254194', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '62521806254194', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 06:44:32
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 04b5c5aa-77f1-4fc1-ad0a-a3dc940eacde\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62521806254194\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-15T06:29:27.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T06:44:30.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-15 06:44:32] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62521806254194', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '62521806254194', status = 'Active';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-15 09:37:43] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 09:37:43
[subscription_update] [2025-08-15 09:37:43] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 09:37:43] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a29b230a-8c5f-4b53-8082-44052e3fd19e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75509849291012\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T09:17:38.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T09:37:41.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 09:37:43] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 09:37:43] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 09:37:43
[subscription_update] [2025-08-15 09:37:43] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 09:37:43] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a29b230a-8c5f-4b53-8082-44052e3fd19e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75509849291012\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T09:17:38.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T09:37:41.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 09:37:43] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 09:37:47] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75509849291012\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-13\n            [endDate] => 2026-08-12\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Jeffery Smith\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Jeffery Smith\n                            [type] => End Customer\n                            [address1] => Flat 1 Norfolk Heights 3 Norfolk\n                            [address2] => Avenue\n                            [address3] => \n                            [city] => Bristol\n                            [stateProvince] => AVON\n                            [postalCode] => BS2 8RL\n                            [country] => United Kingdom\n                            [individualFlag] => 1\n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => \n                            [parentIndustrySegment] => \n                            [primaryAdminFirstName] => Jeffery\n                            [primaryAdminLastName] => Smith\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Jeffery Smith - 5723\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Jeffery\n                            [last] => Smith\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => 1\n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 09:37:47] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: New record inserted, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-15 09:37:51] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75509849291012\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-13\n            [endDate] => 2026-08-12\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Jeffery Smith\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Jeffery Smith\n                            [type] => End Customer\n                            [address1] => Flat 1 Norfolk Heights 3 Norfolk\n                            [address2] => Avenue\n                            [address3] => \n                            [city] => Bristol\n                            [stateProvince] => AVON\n                            [postalCode] => BS2 8RL\n                            [country] => United Kingdom\n                            [individualFlag] => 1\n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => \n                            [parentIndustrySegment] => \n                            [primaryAdminFirstName] => Jeffery\n                            [primaryAdminLastName] => Smith\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Jeffery Smith - 5723\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Jeffery\n                            [last] => Smith\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => 1\n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 09:37:51] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-15 11:35:56] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 11:35:56
[subscription_update] [2025-08-15 11:35:56] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 11:35:56] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => fd7810be-4fbf-4f81-8586-a4f7c1302c9f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75525391314639\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T11:20:48.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T11:35:54.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 11:35:56] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 11:35:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 11:35:57
[subscription_update] [2025-08-15 11:35:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 11:35:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => fd7810be-4fbf-4f81-8586-a4f7c1302c9f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75525391314639\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T11:20:48.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T11:35:54.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 11:35:57] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 11:36:00] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75525391314639\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-15\n            [endDate] => 2026-08-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => PAYPAL\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => \n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => \n                    [description] => \n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Ramsay McMichael Consulting\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Ramsay McMichael Consulting\n                            [type] => End Customer\n                            [address1] => Standard Bldg 102 Hope Street\n                            [address2] => \n                            [address3] => \n                            [city] => Glasgow\n                            [stateProvince] => LANARKSHIRE\n                            [postalCode] => G2 6PH\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Greenfield\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Buildings\n                            [primaryAdminFirstName] => Suzanne\n                            [primaryAdminLastName] => McMichael\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Suzanne McMichael - 2501\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Des\n                            [last] => Quinn\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 11:36:00] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-15 11:36:00] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75525391314639\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-15\n            [endDate] => 2026-08-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => PAYPAL\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => \n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => \n                    [description] => \n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Ramsay McMichael Consulting\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Ramsay McMichael Consulting\n                            [type] => End Customer\n                            [address1] => Standard Bldg 102 Hope Street\n                            [address2] => \n                            [address3] => \n                            [city] => Glasgow\n                            [stateProvince] => LANARKSHIRE\n                            [postalCode] => G2 6PH\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Greenfield\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Buildings\n                            [primaryAdminFirstName] => Suzanne\n                            [primaryAdminLastName] => McMichael\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Suzanne McMichael - 2501\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Des\n                            [last] => Quinn\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 11:36:00] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 23:39:54
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0ffd5d84-499c-48ae-ac1b-808096088ed2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75526291637956\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T23:24:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T23:39:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 23:39:54
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3b98e147-bf09-48f1-8c4e-8ae948f3c4c1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75526302263912\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T23:24:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T23:39:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 23:39:54
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => cb0a9528-7041-41c7-8c93-987ecdbfb57d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75526302267713\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T23:24:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T23:39:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 23:39:54
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0ffd5d84-499c-48ae-ac1b-808096088ed2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75526291637956\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T23:24:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T23:39:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 23:39:54
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => cb0a9528-7041-41c7-8c93-987ecdbfb57d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75526302267713\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T23:24:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T23:39:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 23:39:54
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3b98e147-bf09-48f1-8c4e-8ae948f3c4c1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75526302263912\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T23:24:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T23:39:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 23:39:54
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f731c9f7-a2e1-462b-ac86-496995c72a45\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75526291633555\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T23:24:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T23:39:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 23:39:54] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 23:39:55] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-15 23:39:55
[subscription_update] [2025-08-15 23:39:55] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-15 23:39:55] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f731c9f7-a2e1-462b-ac86-496995c72a45\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75526291633555\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-15T23:24:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-15T23:39:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-15 23:39:55] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-15 23:39:57] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75526302267713\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-15\n            [endDate] => 2026-08-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000125\n            [offeringCode] => COLLRP\n            [offeringName] => BIM Collaborate Pro\n            [marketingName] => BIM Collaborate Pro\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                            [type] => End Customer\n                            [address1] => 46 Conyngham Road\n                            [address2] => \n                            [address3] => \n                            [city] => Northampton\n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => Ricky\n                            [primaryAdminLastName] => Patel\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Ricky Patel - 3414\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Ricky\n                            [last] => Patel\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => 1\n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 23:39:57] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-15 23:39:58] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75526291637956\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-15\n            [endDate] => 2026-08-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000125\n            [offeringCode] => COLLRP\n            [offeringName] => BIM Collaborate Pro\n            [marketingName] => BIM Collaborate Pro\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                            [type] => End Customer\n                            [address1] => 46 Conyngham Road\n                            [address2] => \n                            [address3] => \n                            [city] => Northampton\n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => Ricky\n                            [primaryAdminLastName] => Patel\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Ricky Patel - 3414\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Ricky\n                            [last] => Patel\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => 1\n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 23:39:58] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-15 23:39:58] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75526291633555\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-15\n            [endDate] => 2026-08-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000052\n            [offeringCode] => AECCOL\n            [offeringName] => AEC Collection\n            [marketingName] => Architecture Engineering & Construction Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                            [type] => End Customer\n                            [address1] => 46 Conyngham Road\n                            [address2] => \n                            [address3] => \n                            [city] => Northampton\n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => Ricky\n                            [primaryAdminLastName] => Patel\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Ricky Patel - 3414\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Ricky\n                            [last] => Patel\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => 1\n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 23:39:58] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-15 23:39:58] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75526302267713\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-15\n            [endDate] => 2026-08-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000125\n            [offeringCode] => COLLRP\n            [offeringName] => BIM Collaborate Pro\n            [marketingName] => BIM Collaborate Pro\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                            [type] => End Customer\n                            [address1] => 46 Conyngham Road\n                            [address2] => \n                            [address3] => \n                            [city] => Northampton\n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => Ricky\n                            [primaryAdminLastName] => Patel\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Ricky Patel - 3414\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Ricky\n                            [last] => Patel\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => 1\n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 23:39:58] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-15 23:40:03] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75526302263912\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-15\n            [endDate] => 2026-08-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000052\n            [offeringCode] => AECCOL\n            [offeringName] => AEC Collection\n            [marketingName] => Architecture Engineering & Construction Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                            [type] => End Customer\n                            [address1] => 46 Conyngham Road\n                            [address2] => \n                            [address3] => \n                            [city] => Northampton\n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => Ricky\n                            [primaryAdminLastName] => Patel\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Ricky Patel - 3414\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Ricky\n                            [last] => Patel\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => 1\n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 23:40:03] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-15 23:40:04] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75526291633555\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-15\n            [endDate] => 2026-08-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000052\n            [offeringCode] => AECCOL\n            [offeringName] => AEC Collection\n            [marketingName] => Architecture Engineering & Construction Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                            [type] => End Customer\n                            [address1] => 46 Conyngham Road\n                            [address2] => \n                            [address3] => \n                            [city] => Northampton\n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => Ricky\n                            [primaryAdminLastName] => Patel\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Ricky Patel - 3414\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Ricky\n                            [last] => Patel\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => 1\n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 23:40:04] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-15 23:40:04] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75526302263912\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-15\n            [endDate] => 2026-08-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000052\n            [offeringCode] => AECCOL\n            [offeringName] => AEC Collection\n            [marketingName] => Architecture Engineering & Construction Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                            [type] => End Customer\n                            [address1] => 46 Conyngham Road\n                            [address2] => \n                            [address3] => \n                            [city] => Northampton\n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => Ricky\n                            [primaryAdminLastName] => Patel\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Ricky Patel - 3414\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Ricky\n                            [last] => Patel\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => 1\n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 23:40:04] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75526291637956\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-15\n            [endDate] => 2026-08-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000125\n            [offeringCode] => COLLRP\n            [offeringName] => BIM Collaborate Pro\n            [marketingName] => BIM Collaborate Pro\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Xtinguish Ltd\n                            [type] => End Customer\n                            [address1] => 46 Conyngham Road\n                            [address2] => \n                            [address3] => \n                            [city] => Northampton\n                            [stateProvince] => \n                            [postalCode] => NN3 9TA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => Ricky\n                            [primaryAdminLastName] => Patel\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Ricky Patel - 3414\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Ricky\n                            [last] => Patel\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => 1\n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-15 23:40:04] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-15 23:40:04] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-17 06:07:02
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => feb84a28-6a3f-4a1e-a8c5-c92235baff87\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59680488697967\n            [status] => Expired\n            [quantity] => 5\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-08-17T05:36:58.000+0000\n        )\n\n    [publishedAt] => 2025-08-17T06:07:00.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-17 06:07:02
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => feb84a28-6a3f-4a1e-a8c5-c92235baff87\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59680488697967\n            [status] => Expired\n            [quantity] => 5\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-08-17T05:36:58.000+0000\n        )\n\n    [publishedAt] => 2025-08-17T06:07:00.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59680488697967', status = 'Expired', quantity = 5 ON DUPLICATE KEY UPDATE subscriptionId = '59680488697967', status = 'Expired', quantity = 5;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-17 06:07:02] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59680488697967', status = 'Expired', quantity = 5 ON DUPLICATE KEY UPDATE subscriptionId = '59680488697967', status = 'Expired', quantity = 5;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-17 16:38:14
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5b2562a3-3cc9-4637-8d01-b431fb6530c0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73324048953791\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-17T16:23:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-17T16:38:12.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-17 16:38:14
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5b2562a3-3cc9-4637-8d01-b431fb6530c0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73324048953791\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-17T16:23:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-17T16:38:12.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73324048953791', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73324048953791', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-17 16:38:14] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73324048953791', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73324048953791', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 00:10:59
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3b0bf735-1fda-4b96-813c-5c0199a76768\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56018211103443\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-17T23:40:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T00:10:56.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56018211103443', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '56018211103443', status = 'Suspended';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 00:10:59
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3b0bf735-1fda-4b96-813c-5c0199a76768\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56018211103443\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-17T23:40:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T00:10:56.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 00:10:59] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56018211103443', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '56018211103443', status = 'Suspended';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 08:39:19
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 99b4f49b-7b1a-475f-9002-2d6c852caf70\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59733048340494\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-08-19\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-18T08:24:14.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T08:39:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 08:39:19
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 99b4f49b-7b1a-475f-9002-2d6c852caf70\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59733048340494\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-08-19\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-18T08:24:14.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T08:39:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59733048340494', status = 'Active', quantity = 2, endDate = '2026-08-19', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59733048340494', status = 'Active', quantity = 2, endDate = '2026-08-19', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-18 08:39:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59733048340494', status = 'Active', quantity = 2, endDate = '2026-08-19', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59733048340494', status = 'Active', quantity = 2, endDate = '2026-08-19', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 09:40:45
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => dae2732d-6f08-4ad9-b5e0-fd72899b6d52\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75127504067843\n            [status] => Expired\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-18T09:10:38.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T09:40:42.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75127504067843', status = 'Expired' ON DUPLICATE KEY UPDATE subscriptionId = '75127504067843', status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 09:40:45
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => dae2732d-6f08-4ad9-b5e0-fd72899b6d52\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75127504067843\n            [status] => Expired\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-18T09:10:38.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T09:40:42.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 09:40:45] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75127504067843', status = 'Expired' ON DUPLICATE KEY UPDATE subscriptionId = '75127504067843', status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 10:09:44
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bbbf68f9-176f-4876-97c0-e2bb6e370939\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69460117928610\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-12\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-18T09:49:39.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T10:09:42.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 10:09:44
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bbbf68f9-176f-4876-97c0-e2bb6e370939\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69460117928610\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-12\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-18T09:49:39.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T10:09:42.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69460117928610', status = 'Active', quantity = 1, endDate = '2026-09-12', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69460117928610', status = 'Active', quantity = 1, endDate = '2026-09-12', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 10:09:44] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69460117928610', status = 'Active', quantity = 1, endDate = '2026-09-12', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69460117928610', status = 'Active', quantity = 1, endDate = '2026-09-12', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 10:41:42
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 92dd69fb-1e36-4253-83ea-fd348ee70c24\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56992488105316\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-30\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-18T10:16:36.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T10:41:40.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 10:41:42
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 92dd69fb-1e36-4253-83ea-fd348ee70c24\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56992488105316\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-30\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-18T10:16:36.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T10:41:40.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56992488105316', status = 'Active', quantity = 1, endDate = '2026-09-30', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56992488105316', status = 'Active', quantity = 1, endDate = '2026-09-30', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-18 10:41:42] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56992488105316', status = 'Active', quantity = 1, endDate = '2026-09-30', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56992488105316', status = 'Active', quantity = 1, endDate = '2026-09-30', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:09:53
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b7c8d6e6-d342-4cf9-b8ec-5a75b4ac3f60\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75127504067843\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-18T10:39:48.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:09:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75127504067843', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '75127504067843', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:09:53
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b7c8d6e6-d342-4cf9-b8ec-5a75b4ac3f60\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75127504067843\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-18T10:39:48.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:09:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 11:09:53] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75127504067843', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '75127504067843', status = 'Active';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:35:47
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f1923fc7-7838-4f87-8e12-4d9553e84e04\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56992488105316\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:10:35.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:35:45.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56992488105316', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56992488105316', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:35:47
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f1923fc7-7838-4f87-8e12-4d9553e84e04\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56992488105316\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:10:35.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:35:45.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 11:35:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56992488105316', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56992488105316', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:36:15
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 631f62f8-2b3e-4f85-96d7-e5f745d722a1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59733048340494\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:11:03.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:36:13.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:36:15
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 631f62f8-2b3e-4f85-96d7-e5f745d722a1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59733048340494\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:11:03.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:36:13.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59733048340494', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '59733048340494', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 11:36:15] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59733048340494', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '59733048340494', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 11:37:46] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:37:46
[subscription_update] [2025-08-18 11:37:46] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:37:46] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bb1bd055-663b-4cf0-89b7-43659f2f55e1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75551056564098\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:16:29.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:37:45.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:37:46] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-18 11:37:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:37:47
[subscription_update] [2025-08-18 11:37:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:37:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bb1bd055-663b-4cf0-89b7-43659f2f55e1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75551056564098\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:16:29.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:37:45.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:37:47] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-18 11:37:50] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75551056564098\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 2\n            [status] => Inactive\n            [startDate] => 2025-09-12\n            [endDate] => 2026-09-11\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => SHIRE GROUP BSC Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => SHIRE GROUP BSC Ltd\n                            [type] => End Customer\n                            [address1] => Grosvenor House 11 St. Pauls Square\n                            [address2] => \n                            [address3] => \n                            [city] => Birmingham\n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => B3 1RB\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => David\n                            [primaryAdminLastName] => Woods\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 6796296\n                            [teamName] => David Woods - 6296\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => David\n                            [last] => Woods\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-18 11:37:50] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-18 11:37:51] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75551056564098\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 2\n            [status] => Inactive\n            [startDate] => 2025-09-12\n            [endDate] => 2026-09-11\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => SHIRE GROUP BSC Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => SHIRE GROUP BSC Ltd\n                            [type] => End Customer\n                            [address1] => Grosvenor House 11 St. Pauls Square\n                            [address2] => \n                            [address3] => \n                            [city] => Birmingham\n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => B3 1RB\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => David\n                            [primaryAdminLastName] => Woods\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 6796296\n                            [teamName] => David Woods - 6296\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => David\n                            [last] => Woods\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-18 11:37:51] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-18 11:37:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:37:52
[subscription_update] [2025-08-18 11:37:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:37:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b9463980-2ccb-4840-a786-f708957ec7f5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75551056568699\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:16:29.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:37:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:37:52] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-18 11:37:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:37:52
[subscription_update] [2025-08-18 11:37:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:37:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b9463980-2ccb-4840-a786-f708957ec7f5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75551056568699\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:16:29.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:37:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:37:52] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-18 11:37:55] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75551056568699\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-18\n            [endDate] => 2026-08-17\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000052\n            [offeringCode] => AECCOL\n            [offeringName] => AEC Collection\n            [marketingName] => Architecture Engineering & Construction Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => SHIRE GROUP BSC Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => SHIRE GROUP BSC Ltd\n                            [type] => End Customer\n                            [address1] => Grosvenor House 11 St. Pauls Square\n                            [address2] => \n                            [address3] => \n                            [city] => Birmingham\n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => B3 1RB\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => David\n                            [primaryAdminLastName] => Woods\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 6796296\n                            [teamName] => David Woods - 6296\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => David\n                            [last] => Woods\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-18 11:37:55] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-18 11:37:57] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75551056568699\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-18\n            [endDate] => 2026-08-17\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000052\n            [offeringCode] => AECCOL\n            [offeringName] => AEC Collection\n            [marketingName] => Architecture Engineering & Construction Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => SHIRE GROUP BSC Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => SHIRE GROUP BSC Ltd\n                            [type] => End Customer\n                            [address1] => Grosvenor House 11 St. Pauls Square\n                            [address2] => \n                            [address3] => \n                            [city] => Birmingham\n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => B3 1RB\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => David\n                            [primaryAdminLastName] => Woods\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 6796296\n                            [teamName] => David Woods - 6296\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => David\n                            [last] => Woods\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-18 11:37:57] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:38:29
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3ee3a6db-c6b9-4534-bb76-d8a9e2b436f5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75551499191069\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:17:06.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:38:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:38:29
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3ee3a6db-c6b9-4534-bb76-d8a9e2b436f5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75551499191069\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:17:06.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:38:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:38:29
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f00c6206-7d4d-4749-9d5e-68b402c486ee\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69460117928610\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:13:16.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:38:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 11:38:29] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69460117928610', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69460117928610', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 11:38:30] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-18 11:38:30
[subscription_update] [2025-08-18 11:38:30] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-18 11:38:30] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f00c6206-7d4d-4749-9d5e-68b402c486ee\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69460117928610\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-18T11:13:16.000+0000\n        )\n\n    [publishedAt] => 2025-08-18T11:38:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-18 11:38:30] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-18 11:38:30] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-18 11:38:30] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-18 11:38:30] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-18 11:38:30] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69460117928610', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69460117928610', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-18 11:38:33] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75551499191069\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-18\n            [endDate] => 2026-08-17\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Lester Aldridge\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Lester Aldridge\n                            [type] => End Customer\n                            [address1] => Mountbatten House Grosvenor Square\n                            [address2] => \n                            [address3] => \n                            [city] => Southampton\n                            [stateProvince] => HAMPSHIRE\n                            [postalCode] => SO15 2JU\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => estelle\n                            [primaryAdminLastName] => boullet\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => estelle boullet - 4872\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => estelle\n                            [last] => boullet\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-18 11:38:33] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75551499191069\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-18\n            [endDate] => 2026-08-17\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Lester Aldridge\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Lester Aldridge\n                            [type] => End Customer\n                            [address1] => Mountbatten House Grosvenor Square\n                            [address2] => \n                            [address3] => \n                            [city] => Southampton\n                            [stateProvince] => HAMPSHIRE\n                            [postalCode] => SO15 2JU\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => estelle\n                            [primaryAdminLastName] => boullet\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => estelle boullet - 4872\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => estelle\n                            [last] => boullet\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-18 11:38:33] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-18 11:38:33] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
