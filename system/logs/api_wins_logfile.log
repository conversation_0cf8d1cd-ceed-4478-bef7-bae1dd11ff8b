[api_wins] [2025-08-14 13:09:46] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(32) "api\nav_tree\get_template_fields"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:53] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:53] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:53] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:54] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:54] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:54] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:54] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:54] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:54] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:56] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:09:59] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(27) "api\nav_tree\save_nav_entry"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:10:00] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(36) "api\nav_tree\save_nav_entry_progress"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:10:01] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(36) "api\nav_tree\save_nav_entry_progress"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:10:06] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:10:06] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:10:07] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:10:07] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:10:08] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:10:08] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:10:14] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:10:14] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:12:56] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(26) "api\data_sources\edit_view"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:12:57] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(39) "api\data_sources\query_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:12:57] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(38) "api\data_sources\data_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:14:12] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:14:12] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:14:16] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:14:16] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:43:09] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\delete_nav_entry"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:19] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:19] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:44] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:44] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:47] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(26) "api\nav_tree\add_nav_entry"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:49] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(32) "api\nav_tree\get_template_fields"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:58] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:58] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:58] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:58] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:59] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:59] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:59] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:44:59] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:45:04] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:45:36] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(27) "api\nav_tree\save_nav_entry"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:45:37] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(36) "api\nav_tree\save_nav_entry_progress"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:45:39] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(36) "api\nav_tree\save_nav_entry_progress"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:45:45] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:45:45] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:45:47] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:45:47] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:45:54] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:45:54] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:02] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(26) "api\data_sources\edit_view"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:03] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(39) "api\data_sources\query_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:14] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(37) "api\data_sources\update_query_preview"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:16] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(38) "api\data_sources\data_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:42] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:42] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:45] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:45] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:50] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(26) "api\data_sources\edit_view"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:53] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(39) "api\data_sources\query_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:56] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:56] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:59] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:46:59] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:03] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(29) "api\data_sources\preview_view"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:12] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(26) "api\data_sources\edit_view"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:13] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(39) "api\data_sources\query_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:17] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:18] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:19] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(26) "api\data_sources\edit_view"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:20] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(38) "api\data_sources\data_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:20] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(39) "api\data_sources\query_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:27] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:27] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:31] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(26) "api\data_sources\edit_view"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:32] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(39) "api\data_sources\query_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:34] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(38) "api\data_sources\data_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 13:47:35] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(37) "api\data_sources\update_query_preview"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 15:12:58] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 15:12:58] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 15:13:00] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 15:13:01] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 15:30:31] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 15:30:31] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 15:47:55] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(6) "system"\n    [1]: string(13) "database_dump"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 15:48:26] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(6) "system"\n    [1]: string(13) "database_dump"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 19:42:55] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(6) "system"\n    [1]: string(13) "database_dump"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 20:01:00] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(6) "system"\n    [1]: string(13) "database_dump"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 20:15:52] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 20:28:14] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:13:56] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:14:13] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:14:18] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:30:01] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:31:46] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:46:12] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:46:58] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:49:33] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:51:48] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:52:17] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:54:02] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:55:34] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:56:11] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:56:34] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 21:56:41] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 22:07:56] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 22:09:40] [layout-api.edge.php:54]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 54\narray(3) {\n  ["namespace"]: string(25) "api\system\database_dump\"\n  ["function_call"]: string(46) "api\system\database_dump\dump_autobooks_tables"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(21) "dump_autobooks_tables"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/syste...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-14 22:23:20] [layout-api.edge.php:54]  
[api_wins] [2025-08-14 22:25:57] [layout-api.edge.php:54]  
