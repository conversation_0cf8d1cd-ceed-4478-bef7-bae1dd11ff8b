-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 15, 2025 at 07:45 AM
-- Table: autodesk_subscriptions_unsub
-- Records: 6

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autodesk_subscriptions_unsub`
--

DROP TABLE IF EXISTS `autodesk_subscriptions_unsub`;
CREATE TABLE `autodesk_subscriptions_unsub` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subscriptionId` varchar(50) DEFAULT NULL,
  `subscriptionReferenceNumber` varchar(255) DEFAULT NULL,
  `tcs_unsubscribe` int(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_modified` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subscriptionId` (`subscriptionId`,`subscriptionReferenceNumber`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

-- Dumping data for table `autodesk_subscriptions_unsub`
--

INSERT INTO `autodesk_subscriptions_unsub` (`id`, `subscriptionId`, `subscriptionReferenceNumber`, `tcs_unsubscribe`, `created_at`, `last_modified`) VALUES
('1', '71930963170248', '574-88294767', '1', '2025-03-30 23:13:09', '2025-04-05 18:41:39'),
('2', '71932610877074', '574-88337527', '1', '2025-03-30 23:13:09', '2025-04-05 18:41:39'),
('3', '67897718329846', '573-65497519', '1', '2025-03-30 23:13:09', '2025-04-05 18:41:43'),
('4', NULL, '565-84634651', '1', '2025-03-30 23:13:09', '2025-04-05 18:51:24'),
('5', '55387647653314', '566-16544483', '1', '2025-03-30 23:13:09', '2025-04-05 18:41:54'),
('6', '71688672088327', '574-83863352', '1', '2025-03-30 23:13:09', '2025-04-05 18:41:55');

COMMIT;
