[table_config_manager] [2025-08-14 13:10:01] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-14 13:10:01] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-14 13:10:01] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-14 13:45:39] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-14 13:45:39] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-14 13:45:39] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 08:17:16] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 08:17:16] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 08:17:16] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 08:56:32] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 08:56:32] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 08:56:32] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 09:50:43] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 09:50:43] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 09:50:43] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 10:20:28] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 10:20:28] [table_config_manager.class.php:182] Legacy config updated for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-15 10:20:28] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 08:44:45] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 08:44:45] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 08:44:45] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 08:46:00] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 08:46:00] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 08:46:00] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 11:54:09] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 11:54:09] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 11:54:09] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 17:38:23] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 17:38:23] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 17:38:23] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 18:38:20] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 18:38:20] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-16 18:38:20] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-17 19:57:24] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-17 19:57:24] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-17 19:57:24] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-17 20:20:01] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-17 20:20:01] [table_config_manager.class.php:182] Legacy config updated for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-17 20:20:01] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-17 20:20:28] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_sketchup_data, route: enhanced_autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 20:20:28] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 20:20:28] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 20:42:51] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_sketchup_data, route: enhanced_autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 20:42:51] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 20:42:51] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 21:17:57] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-17 21:17:57] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-17 21:17:57] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-17 21:25:07] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_sketchup_data, route: enhanced_autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 21:25:08] [table_config_manager.class.php:182] Legacy config updated for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 21:25:08] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 22:40:24] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_sketchup_data, route: enhanced_autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 22:40:24] [table_config_manager.class.php:182] Legacy config created for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 22:40:24] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 22:43:42] [table_config_manager.class.php:64]  store_table_config called for table: autobooks_import_sketchup_data, route: enhanced_autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 22:43:42] [table_config_manager.class.php:182] Legacy config updated for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-17 22:43:42] [table_config_manager.class.php:203] Unified config stored for: autobooks_import_sketchup_data
