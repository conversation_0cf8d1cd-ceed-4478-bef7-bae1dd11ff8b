[navigation] [2025-08-14 13:09:59] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755176999.csv
[navigation] [2025-08-14 13:10:00] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755176999.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-14 13:10:01] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-14 13:10:01] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-14 13:43:09] [nav_tree.api.php:987] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-14 13:45:36] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755179136.csv
[navigation] [2025-08-14 13:45:37] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755179136.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-14 13:45:39] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-14 13:45:39] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-15 08:06:37] [nav_tree.api.php:987] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-15 08:17:14] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755245834.csv
[navigation] [2025-08-15 08:17:15] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755245834.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-15 08:17:16] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-15 08:17:16] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-15 08:17:49] [nav_tree.api.php:987] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-15 08:20:58] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-15 08:56:30] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755248190.csv
[navigation] [2025-08-15 08:56:31] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755248190.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-15 08:56:32] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-15 08:56:32] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-15 09:48:18] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-15 09:50:41] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755251441.csv
[navigation] [2025-08-15 09:50:42] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755251441.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-15 10:20:27] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755253227.csv
[navigation] [2025-08-15 10:20:28] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755253227.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-15 10:20:28] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-15 10:20:28] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-16 08:44:22] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-16 08:44:43] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755333883.csv
[navigation] [2025-08-16 08:44:44] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755333883.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-16 08:44:45] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-16 08:44:45] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-16 08:45:43] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-16 08:45:58] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755333958.csv
[navigation] [2025-08-16 08:45:59] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755333958.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-16 08:46:00] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-16 08:46:00] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-16 11:53:52] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-16 11:54:07] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755345247.csv
[navigation] [2025-08-16 11:54:08] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755345247.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-16 11:54:09] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-16 11:54:09] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-16 17:37:07] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-16 17:38:21] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755365901.csv
[navigation] [2025-08-16 17:38:22] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755365901.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-16 17:38:23] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-16 17:38:23] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-16 18:37:50] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-16 18:38:18] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755369498.csv
[navigation] [2025-08-16 18:38:19] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755369498.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-16 18:38:20] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-16 18:38:20] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-17 19:03:13] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-17 19:57:22] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755460642.csv
[navigation] [2025-08-17 19:57:23] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755460642.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 19:57:24] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-17 20:14:44] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755461684.csv
[navigation] [2025-08-17 20:14:45] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755461684.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 20:17:57] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755461877.csv
[navigation] [2025-08-17 20:17:58] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755461877.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 20:19:59] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755461999.csv
[navigation] [2025-08-17 20:20:00] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755461999.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 20:20:01] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-17 20:20:01] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-17 20:20:26] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755462026.csv
[navigation] [2025-08-17 20:20:27] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755462026.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 20:20:28] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-17 20:20:28] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-17 20:41:41] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_sketchup_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-17 20:42:11] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755463331.csv
[navigation] [2025-08-17 20:42:12] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755463331.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 20:42:49] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755463369.csv
[navigation] [2025-08-17 20:42:50] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755463369.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 21:17:29] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-17 21:17:55] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755465475.csv
[navigation] [2025-08-17 21:17:56] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755465475.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 21:17:57] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-17 21:17:57] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-17 21:18:41] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755465521.csv
[navigation] [2025-08-17 21:18:42] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755465521.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 21:25:06] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755465906.csv
[navigation] [2025-08-17 21:25:07] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755465906.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 21:25:08] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-17 21:25:08] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => sketchup\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-17 22:39:55] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => sketchup\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_sketchup_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-17 22:40:23] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755470423.csv
[navigation] [2025-08-17 22:40:24] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755470423.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 22:43:40] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755470620.csv
[navigation] [2025-08-17 22:43:41] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755470620.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-17 22:43:42] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-17 22:43:42] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
