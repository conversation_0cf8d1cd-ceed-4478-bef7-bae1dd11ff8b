[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:460] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:71]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:120] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:120] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:120] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:120] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 11:44:35] [unified_field_mapper.class.php:397] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:01:04] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:01:05] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:01:06] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:04:31] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:15:39] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:36:31] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 19:54:32] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 21:07:13] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-16 21:09:56] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:535] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:88]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped serial_number -> subscription_reference (priority: 0, score: 94) -> subscription_reference, subs_subscriptionReferenceNumber, subscription_id
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped name -> company_name (priority: 2, score: 67.4) -> company_name, endcust_name, end_customer_name
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped product_name -> product_name (priority: 1, score: 79) -> product_name, subs_offeringName, agreement_program_name
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped quantity -> quantity (priority: 10, score: 58) -> quantity\n, subs_quantity\n, subscription_quantity
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped end_date -> end_date (priority: 10, score: 58) -> end_date\n, subs_endDate\n, agreement_end_date
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped account_primary_reseller_name -> reseller_name (priority: 3, score: 77.5) -> reseller_name, partner_name, account_primary_reseller_name
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped order_shipping_address -> address (priority: 10, score: 67) -> address\n, end_customer_address_1
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped order_shipping_city -> city (priority: 10, score: 67) -> city\n, end_customer_city
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped order_shipping_state_province -> state (priority: 10, score: 67) -> state\n, end_customer_state
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:137] Mapped order_shipping_country -> country (priority: 10, score: 67) -> country\n, end_customer_country
[unified_field_mapper] [2025-08-17 00:35:41] [unified_field_mapper.class.php:472] Date processing: 29/05/2026 -> 285 days remaining
