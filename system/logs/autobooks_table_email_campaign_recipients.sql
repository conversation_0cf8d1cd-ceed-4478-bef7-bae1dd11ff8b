-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 15, 2025 at 07:45 AM
-- Table: email_campaign_recipients
-- Records: 0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `email_campaign_recipients`
--

DROP TABLE IF EXISTS `email_campaign_recipients`;
CREATE TABLE `email_campaign_recipients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `campaign_id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `recipient_type` enum('customer','subscription','manual','imported') DEFAULT 'manual',
  `reference_id` varchar(100) DEFAULT NULL COMMENT 'Reference to customer/subscription ID',
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Additional recipient data for personalization',
  `is_active` tinyint(1) DEFAULT 1,
  `unsubscribed` tinyint(1) DEFAULT 0,
  `unsubscribed_at` timestamp NULL DEFAULT NULL,
  `added_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_campaign_email` (`campaign_id`,`email`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_email` (`email`),
  KEY `idx_recipient_type` (`recipient_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_unsubscribed` (`unsubscribed`),
  CONSTRAINT `email_campaign_recipients_ibfk_1` FOREIGN KEY (`campaign_id`) REFERENCES `email_campaigns` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Campaign recipient management';

COMMIT;
