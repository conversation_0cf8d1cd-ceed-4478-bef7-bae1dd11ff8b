[data_source_debug] [2025-08-14 13:12:57] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-14 13:12:57] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-14 13:12:57] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-14 13:12:57] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-14 13:12:57] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-14 13:12:57] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-14 13:12:57] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-14 13:12:57] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-14 13:46:03] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: null
[data_source_debug] [2025-08-14 13:46:03] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-14 13:46:03] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-14 13:46:03] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-14 13:46:03] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-14 13:46:03] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-14 13:46:03] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-14 13:46:03] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-14 13:46:14] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: null
[data_source_debug] [2025-08-14 13:46:14] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-14 13:46:14] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-14 13:46:14] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-14 13:46:14] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-14 13:46:14] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-14 13:46:14] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-14 13:46:14] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-14 13:46:53] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: null
[data_source_debug] [2025-08-14 13:46:53] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-14 13:46:53] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-14 13:46:53] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-14 13:46:53] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-14 13:46:53] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-14 13:46:53] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-14 13:46:53] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-14 13:47:13] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: null
[data_source_debug] [2025-08-14 13:47:13] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-14 13:47:13] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-14 13:47:13] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-14 13:47:13] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-14 13:47:13] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-14 13:47:13] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-14 13:47:13] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-14 13:47:20] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-14 13:47:20] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_accounts"]
[data_source_debug] [2025-08-14 13:47:20] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-14 13:47:20] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autodesk_accounts.id","autodesk_accounts.account_csn","autodesk_accounts.name","autodesk_accounts.first_name","autodesk_accounts.last_name","autodesk_accounts.email","autodesk_accounts.phone","autodesk_accounts.account_type","autodesk_accounts.city","autodesk_accounts.postal_code","autodesk_accounts.country"]
[data_source_debug] [2025-08-14 13:47:20] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_accounts"]
[data_source_debug] [2025-08-14 13:47:20] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-14 13:47:20] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autodesk_accounts":["id","account_csn","name","first_name","last_name","email","phone","account_type","city","postal_code","country"]}
[data_source_debug] [2025-08-14 13:47:20] [data_sources.api.php:1767] query_preview_fragment - final filters: [{"column":"name","operator":"LIKE","value":"Ltd"},{"column":"country","operator":"=","value":"United Kingdom"},{"column":"account_type","operator":"=","value":"Customer"}]
[data_source_debug] [2025-08-14 13:47:32] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: null
[data_source_debug] [2025-08-14 13:47:32] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-14 13:47:32] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-14 13:47:32] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-14 13:47:32] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-14 13:47:32] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-14 13:47:32] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-14 13:47:32] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-14 13:47:35] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: null
[data_source_debug] [2025-08-14 13:47:35] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-14 13:47:35] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-14 13:47:35] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-14 13:47:35] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-14 13:47:35] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-14 13:47:35] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-14 13:47:35] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 07:51:31] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: null
[data_source_debug] [2025-08-15 07:51:31] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-15 07:51:31] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 07:51:31] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-15 07:51:31] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-15 07:51:31] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 07:51:31] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-15 07:51:31] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 08:57:02] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: null
[data_source_debug] [2025-08-15 08:57:02] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-15 08:57:02] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 08:57:02] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-15 08:57:02] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-15 08:57:02] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 08:57:02] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-15 08:57:02] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 09:05:02] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 09:05:02] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 09:05:02] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 09:05:02] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-15 09:05:02] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 09:05:02] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"}]
[data_source_debug] [2025-08-15 09:05:02] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-15 09:05:02] [data_sources.api.php:1767] query_preview_fragment - final filters: [{"column":"autoRenew","operator":"=","value":"ON"},{"column":"status","operator":"=","value":"Active"}]
[data_source_debug] [2025-08-15 09:05:11] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 09:05:11] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:05:11] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 09:05:11] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-15 09:05:11] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:05:11] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 09:05:11] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-15 09:05:11] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:3129] update_column_selection - selected_tables param: [
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:3130] update_column_selection - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:3131] update_column_selection - final selected_tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract"]
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name","contract"]}
[data_source_debug] [2025-08-15 09:26:51] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:3129] update_column_selection - selected_tables param: [
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:3130] update_column_selection - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:3131] update_column_selection - final selected_tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name","contract","product_name"]}
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:3129] update_column_selection - selected_tables param: [
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:3130] update_column_selection - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:3131] update_column_selection - final selected_tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name","contract","product_name","quantity"]}
[data_source_debug] [2025-08-15 09:26:52] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:3129] update_column_selection - selected_tables param: [
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:3130] update_column_selection - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:3131] update_column_selection - final selected_tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity"]
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date"]
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name","contract","product_name","quantity","end_date"]}
[data_source_debug] [2025-08-15 09:26:53] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:3129] update_column_selection - selected_tables param: [
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:3130] update_column_selection - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:3131] update_column_selection - final selected_tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date"]
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name"]
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name"]}
[data_source_debug] [2025-08-15 09:26:54] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 10:12:48] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 10:12:48] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-15 10:12:48] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: null
[data_source_debug] [2025-08-15 10:12:48] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-15 10:12:48] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-15 10:12:48] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 10:12:48] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-15 10:12:48] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 10:14:36] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 10:14:36] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-15 10:14:36] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: null
[data_source_debug] [2025-08-15 10:14:36] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-15 10:14:36] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-15 10:14:36] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 10:14:36] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-15 10:14:36] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 10:19:05] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 10:19:05] [data_sources.api.php:1568] query_preview_fragment - tables param: []
[data_source_debug] [2025-08-15 10:19:05] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: null
[data_source_debug] [2025-08-15 10:19:05] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-15 10:19:05] [data_sources.api.php:1764] query_preview_fragment - final tables: []
[data_source_debug] [2025-08-15 10:19:05] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 10:19:05] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-15 10:19:05] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 10:20:43] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 10:20:43] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 10:20:43] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 10:20:43] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_debug] [2025-08-15 10:20:43] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 10:20:43] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 10:20:43] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country"]}
[data_source_debug] [2025-08-15 10:20:43] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 12:21:06] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:06] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:06] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 12:21:06] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:06] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:06] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 12:21:06] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 12:21:06] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 12:21:26] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:26] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:26] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 12:21:26] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:26] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:26] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 12:21:26] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 12:21:26] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 12:21:28] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:28] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:28] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 12:21:28] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:28] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:28] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 12:21:28] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 12:21:28] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 12:21:29] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:29] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:29] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 12:21:29] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:29] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:29] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 12:21:29] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 12:21:29] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 12:21:33] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:33] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:33] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 12:21:33] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:33] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:33] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 12:21:33] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 12:21:33] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:3129] update_column_selection - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:3130] update_column_selection - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:3131] update_column_selection - final selected_tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name","soldto.id"]
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"],"soldto":["id"]}
[data_source_debug] [2025-08-15 12:21:37] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:3129] update_column_selection - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:3130] update_column_selection - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:3131] update_column_selection - final selected_tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name","soldto.id"]
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 12:21:38] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 12:21:44] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:44] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:44] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:44] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:44] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:44] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 12:21:44] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 12:21:44] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 12:21:48] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:48] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:48] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:48] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:48] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:48] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 12:21:48] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 12:21:48] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 12:21:51] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 12:21:51] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:51] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:51] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 12:21:51] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 12:21:51] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 12:21:51] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 12:21:51] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 13:49:16] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 13:49:16] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:16] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 13:49:16] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 13:49:16] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:16] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 13:49:16] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 13:49:16] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 13:49:25] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 13:49:25] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:25] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 13:49:25] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 13:49:25] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:25] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 13:49:25] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 13:49:25] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 13:49:28] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 13:49:29] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 13:49:29] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:29] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 13:49:29] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 13:49:29] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:29] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 13:49:29] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 13:49:29] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 13:49:42] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 13:49:42] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:42] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 13:49:42] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name"]
[data_source_debug] [2025-08-15 13:49:42] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-15 13:49:42] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.soldTo_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"soldto"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.solutionProvider_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"solpro"},{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"resell"}]
[data_source_debug] [2025-08-15 13:49:42] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"]}
[data_source_debug] [2025-08-15 13:49:42] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-15 19:38:48] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-15 19:38:48] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 19:38:48] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-15 19:38:48] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_debug] [2025-08-15 19:38:48] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-15 19:38:48] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-15 19:38:48] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country"]}
[data_source_debug] [2025-08-15 19:38:48] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-16 08:46:59] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-16 08:46:59] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-16 08:46:59] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-16 08:46:59] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_debug] [2025-08-16 08:46:59] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-16 08:46:59] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-16 08:46:59] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country"]}
[data_source_debug] [2025-08-16 08:46:59] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-16 09:10:50] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-16 09:10:50] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-16 09:10:50] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-16 09:10:50] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_debug] [2025-08-16 09:10:50] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-16 09:10:50] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-16 09:10:50] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country"]}
[data_source_debug] [2025-08-16 09:10:50] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 18:51:41] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 18:51:41] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-17 18:51:41] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 18:51:41] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["subs.id","subs.subscriptionId","subs.subscriptionReferenceNumber","subs.quantity","subs.status","subs.startDate","subs.endDate","endcust.id","endcust.account_csn","endcust.name","endcust.first_name","endcust.last_name","lastquote.quote_id","lastquote.quote_number","lastquote.quoted_date"]
[data_source_debug] [2025-08-17 18:51:41] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autodesk_subscriptions","autodesk_accounts"]
[data_source_debug] [2025-08-17 18:51:41] [data_sources.api.php:1765] query_preview_fragment - final joins: [{"type":"LEFT","table":"autodesk_accounts","left_column":"autodesk_subscriptions.endCustomer_csn","right_column":"autodesk_accounts.account_csn","left_table":"autodesk_subscriptions","right_table":"autodesk_accounts","left_alias":"","right_alias":"endcust"}]
[data_source_debug] [2025-08-17 18:51:41] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"subs":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate"],"endcust":["id","account_csn","name","first_name","last_name"],"lastquote":["quote_id","quote_number","quoted_date"]}
[data_source_debug] [2025-08-17 18:51:41] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 20:27:36] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 20:27:36] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 20:27:36] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 20:27:36] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_sketchup_data.sold_to_name","autobooks_import_sketchup_data.sold_to_number","autobooks_import_sketchup_data.vendor_name","autobooks_import_sketchup_data.reseller_number","autobooks_import_sketchup_data.reseller_vendor_id","autobooks_import_sketchup_data.end_customer_vendor_id","autobooks_import_sketchup_data.end_customer_name","autobooks_import_sketchup_data.end_customer_address_1","autobooks_import_sketchup_data.end_customer_address_2","autobooks_import_sketchup_data.end_customer_address_3","autobooks_import_sketchup_data.end_customer_city","autobooks_import_sketchup_data.end_customer_state","autobooks_import_sketchup_data.end_customer_zip_code","autobooks_import_sketchup_data.end_customer_country","autobooks_import_sketchup_data.end_customer_account_type","autobooks_import_sketchup_data.end_customer_contact_name","autobooks_import_sketchup_data.end_customer_contact_email","autobooks_import_sketchup_data.end_customer_contact_phone","autobooks_import_sketchup_data.end_customer_industry_segment","autobooks_import_sketchup_data.agreement_program_name","autobooks_import_sketchup_data.agreement_number","autobooks_import_sketchup_data.agreement_start_date","autobooks_import_sketchup_data.agreement_end_date","autobooks_import_sketchup_data.agreement_terms","autobooks_import_sketchup_data.agreement_type","autobooks_import_sketchup_data.agreement_status","autobooks_import_sketchup_data.agreement_support_level","autobooks_import_sketchup_data.agreement_days_due","autobooks_import_sketchup_data.agreement_autorenew","autobooks_import_sketchup_data.product_name","autobooks_import_sketchup_data.product_family","autobooks_import_sketchup_data.product_market_segment","autobooks_import_sketchup_data.product_release","autobooks_import_sketchup_data.product_type","autobooks_import_sketchup_data.product_deployment","autobooks_import_sketchup_data.product_sku","autobooks_import_sketchup_data.product_sku_description","autobooks_import_sketchup_data.product_part","autobooks_import_sketchup_data.product_list_price","autobooks_import_sketchup_data.product_list_price_currency","autobooks_import_sketchup_data.subscription_id","autobooks_import_sketchup_data.subscription_serial_number","autobooks_import_sketchup_data.subscription_status","autobooks_import_sketchup_data.subscription_quantity","autobooks_import_sketchup_data.subscription_start_date","autobooks_import_sketchup_data.subscription_end_date","autobooks_import_sketchup_data.subscription_contact_name","autobooks_import_sketchup_data.subscription_contact_email","autobooks_import_sketchup_data.subscription_level","autobooks_import_sketchup_data.subscription_days_due","autobooks_import_sketchup_data.quotation_id","autobooks_import_sketchup_data.quotation_type","autobooks_import_sketchup_data.quotation_vendor_id","autobooks_import_sketchup_data.quotation_deal_registration_number","autobooks_import_sketchup_data.quotation_status","autobooks_import_sketchup_data.quotation_resellerpo_previous","autobooks_import_sketchup_data.quotation_due_date","autobooks_import_sketchup_data.flaer_phase","autobooks_import_sketchup_data.updated"]
[data_source_debug] [2025-08-17 20:27:36] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 20:27:36] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 20:27:36] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_sketchup_data":["sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated"]}
[data_source_debug] [2025-08-17 20:27:36] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 20:45:27] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 20:45:27] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:45:27] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 20:45:27] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_debug] [2025-08-17 20:45:27] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:45:27] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 20:45:27] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country"]}
[data_source_debug] [2025-08-17 20:45:27] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 20:45:48] [data_sources.api.php:3135] update_column_selection - selected_tables param: [
[data_source_debug] [2025-08-17 20:45:48] [data_sources.api.php:3136] update_column_selection - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:45:48] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country","autobooks_import_bluebeam_data.created_at"]
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country","created_at"]}
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:3135] update_column_selection - selected_tables param: [
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:3136] update_column_selection - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country","autobooks_import_bluebeam_data.created_at"]
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country","autobooks_import_bluebeam_data.created_at","autobooks_import_bluebeam_data.updated_at"]
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country","created_at","updated_at"]}
[data_source_debug] [2025-08-17 20:45:49] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 20:46:07] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 20:46:07] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:46:07] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 20:46:07] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country","autobooks_import_bluebeam_data.created_at","autobooks_import_bluebeam_data.updated_at"]
[data_source_debug] [2025-08-17 20:46:07] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:46:07] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 20:46:07] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country","created_at","updated_at"]}
[data_source_debug] [2025-08-17 20:46:07] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 20:47:22] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 20:47:22] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:47:22] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 20:47:22] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country","autobooks_import_bluebeam_data.created_at","autobooks_import_bluebeam_data.updated_at"]
[data_source_debug] [2025-08-17 20:47:22] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:47:22] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 20:47:22] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country","created_at","updated_at"]}
[data_source_debug] [2025-08-17 20:47:22] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 20:47:37] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 20:47:37] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:47:37] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 20:47:37] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-17 20:47:37] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-17 20:47:37] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 20:47:37] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-17 20:47:37] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 21:25:25] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 21:25:25] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 21:25:25] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 21:25:25] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-17 21:25:25] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 21:25:25] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 21:25:25] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-17 21:25:25] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 21:55:50] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 21:55:50] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 21:55:50] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 21:55:50] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-17 21:55:50] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 21:55:50] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 21:55:50] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-17 21:55:50] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 22:30:49] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 22:30:49] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 22:30:49] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 22:30:49] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-17 22:30:49] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 22:30:49] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 22:30:49] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-17 22:30:49] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 22:44:11] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 22:44:11] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 22:44:11] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 22:44:11] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-17 22:44:11] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 22:44:11] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 22:44:11] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-17 22:44:11] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-17 22:52:20] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-17 22:52:20] [data_sources.api.php:1568] query_preview_fragment - tables param: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 22:52:20] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-17 22:52:20] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["sketchup.sold_to_name","sketchup.sold_to_number","sketchup.vendor_name","sketchup.reseller_number","sketchup.reseller_vendor_id","sketchup.end_customer_vendor_id","sketchup.end_customer_name","sketchup.end_customer_address_1","sketchup.end_customer_address_2","sketchup.end_customer_address_3","sketchup.end_customer_city","sketchup.end_customer_state","sketchup.end_customer_zip_code","sketchup.end_customer_country","sketchup.end_customer_account_type","sketchup.end_customer_contact_name","sketchup.end_customer_contact_email","sketchup.end_customer_contact_phone","sketchup.end_customer_industry_segment","sketchup.agreement_program_name","sketchup.agreement_number","sketchup.agreement_start_date","sketchup.agreement_end_date","sketchup.agreement_terms","sketchup.agreement_type","sketchup.agreement_status","sketchup.agreement_support_level","sketchup.agreement_days_due","sketchup.agreement_autorenew","sketchup.product_name","sketchup.product_family","sketchup.product_market_segment","sketchup.product_release","sketchup.product_type","sketchup.product_deployment","sketchup.product_sku","sketchup.product_sku_description","sketchup.product_part","sketchup.product_list_price","sketchup.product_list_price_currency","sketchup.subscription_id","sketchup.subscription_serial_number","sketchup.subscription_status","sketchup.subscription_quantity","sketchup.subscription_start_date","sketchup.subscription_end_date","sketchup.subscription_contact_name","sketchup.subscription_contact_email","sketchup.subscription_level","sketchup.subscription_days_due","sketchup.quotation_id","sketchup.quotation_type","sketchup.quotation_vendor_id","sketchup.quotation_deal_registration_number","sketchup.quotation_status","sketchup.quotation_resellerpo_previous","sketchup.quotation_due_date","sketchup.flaer_phase","sketchup.updated"]
[data_source_debug] [2025-08-17 22:52:20] [data_sources.api.php:1764] query_preview_fragment - final tables: ["autobooks_import_sketchup_data"]
[data_source_debug] [2025-08-17 22:52:20] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-17 22:52:20] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"sketchup":["sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated"]}
[data_source_debug] [2025-08-17 22:52:20] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string"]
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["unique_hash","hash_string"]}
[data_source_debug] [2025-08-18 08:11:07] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string"]
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.hash_string"]
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["hash_string"]}
[data_source_debug] [2025-08-18 08:11:08] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.hash_string"]
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["hash_string"]}
[data_source_debug] [2025-08-18 08:11:09] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string"]
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName"]
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash","hash_string","offeringName"]}
[data_source_debug] [2025-08-18 08:11:11] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash","hash_string","offeringName","offeringCode"]}
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash","hash_string","offeringName","offeringCode","offeringId"]}
[data_source_debug] [2025-08-18 08:11:12] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId"]
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code"]
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash","hash_string","offeringName","offeringCode","offeringId","intendedUsage_code"]}
[data_source_debug] [2025-08-18 08:11:13] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code"]
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description"]
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash","hash_string","offeringName","offeringCode","offeringId","intendedUsage_code","intendedUsage_description"]}
[data_source_debug] [2025-08-18 08:11:14] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description"]
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description","products_autodesk_catalog.accessModel_code"]
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash","hash_string","offeringName","offeringCode","offeringId","intendedUsage_code","intendedUsage_description","accessModel_code"]}
[data_source_debug] [2025-08-18 08:11:16] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description","products_autodesk_catalog.accessModel_code"]
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description","products_autodesk_catalog.accessModel_code","products_autodesk_catalog.accessModel_description"]
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash","hash_string","offeringName","offeringCode","offeringId","intendedUsage_code","intendedUsage_description","accessModel_code","accessModel_description"]}
[data_source_debug] [2025-08-18 08:11:18] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description","products_autodesk_catalog.accessModel_code","products_autodesk_catalog.accessModel_description"]
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description","products_autodesk_catalog.accessModel_code","products_autodesk_catalog.accessModel_description","products_autodesk_catalog.servicePlan_code"]
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash","hash_string","offeringName","offeringCode","offeringId","intendedUsage_code","intendedUsage_description","accessModel_code","accessModel_description","servicePlan_code"]}
[data_source_debug] [2025-08-18 08:11:19] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description","products_autodesk_catalog.accessModel_code","products_autodesk_catalog.accessModel_description","products_autodesk_catalog.servicePlan_code"]
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description","products_autodesk_catalog.accessModel_code","products_autodesk_catalog.accessModel_description","products_autodesk_catalog.servicePlan_code","products_autodesk_catalog.servicePlan_description"]
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash","hash_string","offeringName","offeringCode","offeringId","intendedUsage_code","intendedUsage_description","accessModel_code","accessModel_description","servicePlan_code","servicePlan_description"]}
[data_source_debug] [2025-08-18 08:11:20] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description","products_autodesk_catalog.accessModel_code","products_autodesk_catalog.accessModel_description","products_autodesk_catalog.servicePlan_code","products_autodesk_catalog.servicePlan_description"]
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string","products_autodesk_catalog.offeringName","products_autodesk_catalog.offeringCode","products_autodesk_catalog.offeringId","products_autodesk_catalog.intendedUsage_code","products_autodesk_catalog.intendedUsage_description","products_autodesk_catalog.accessModel_code","products_autodesk_catalog.accessModel_description","products_autodesk_catalog.servicePlan_code","products_autodesk_catalog.servicePlan_description","products_autodesk_catalog.connectivity_code"]
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash","hash_string","offeringName","offeringCode","offeringId","intendedUsage_code","intendedUsage_description","accessModel_code","accessModel_description","servicePlan_code","servicePlan_description","connectivity_code"]}
[data_source_debug] [2025-08-18 08:11:21] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string"]
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash"]
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash"]}
[data_source_debug] [2025-08-18 08:16:33] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash"]
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id"]
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id"]}
[data_source_debug] [2025-08-18 08:16:34] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:3135] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:3136] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:3137] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:1567] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:1568] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:1569] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id"]
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:1570] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:1764] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:1765] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:1766] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id"]}
[data_source_debug] [2025-08-18 08:16:35] [data_sources.api.php:1767] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:3036] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:3037] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:3038] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:1495] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:1496] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:1497] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash","products_autodesk_catalog.hash_string"]
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:1498] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash"]
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:1692] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:1693] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:1694] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id","unique_hash"]}
[data_source_debug] [2025-08-18 08:30:00] [data_sources.api.php:1695] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:3036] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:3037] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:3038] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1495] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1496] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1497] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id","products_autodesk_catalog.unique_hash"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1498] query_preview_fragment - selected_columns param: ["products_autodesk_catalog.id"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1692] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1693] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1694] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id"]}
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1695] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:3036] update_column_selection - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:3037] update_column_selection - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:3038] update_column_selection - final selected_tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1495] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1496] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1497] query_preview_fragment - selected_columns_json param: ["products_autodesk_catalog.id"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1498] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1692] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1693] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1694] query_preview_fragment - final selected_columns: {"products_autodesk_catalog":["id"]}
[data_source_debug] [2025-08-18 08:30:02] [data_sources.api.php:1695] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:30:10] [data_sources.api.php:1495] query_preview_fragment - selected_tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:10] [data_sources.api.php:1496] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:10] [data_sources.api.php:1497] query_preview_fragment - selected_columns_json param: []
[data_source_debug] [2025-08-18 08:30:10] [data_sources.api.php:1498] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-18 08:30:10] [data_sources.api.php:1692] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:10] [data_sources.api.php:1693] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:30:10] [data_sources.api.php:1694] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-18 08:30:10] [data_sources.api.php:1695] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-18 08:30:49] [data_sources.api.php:1495] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-18 08:30:49] [data_sources.api.php:1496] query_preview_fragment - tables param: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:49] [data_sources.api.php:1497] query_preview_fragment - selected_columns_json param: []
[data_source_debug] [2025-08-18 08:30:49] [data_sources.api.php:1498] query_preview_fragment - selected_columns param: []
[data_source_debug] [2025-08-18 08:30:49] [data_sources.api.php:1692] query_preview_fragment - final tables: ["products_autodesk_catalog"]
[data_source_debug] [2025-08-18 08:30:49] [data_sources.api.php:1693] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-18 08:30:49] [data_sources.api.php:1694] query_preview_fragment - final selected_columns: []
[data_source_debug] [2025-08-18 08:30:49] [data_sources.api.php:1695] query_preview_fragment - final filters: []
