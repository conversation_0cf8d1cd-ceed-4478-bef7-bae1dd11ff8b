-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 15, 2025 at 07:45 AM
-- Table: autobooks_column_preferences
-- Records: 0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autobooks_column_preferences`
--

DROP TABLE IF EXISTS `autobooks_column_preferences`;
CREATE TABLE `autobooks_column_preferences` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `table_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Data table identifier',
  `user_id` int(10) unsigned DEFAULT NULL COMMENT 'User ID (NULL for global preferences)',
  `data_source_id` int(10) unsigned DEFAULT NULL COMMENT 'Associated data source ID',
  `hidden_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of hidden column IDs',
  `column_structure` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of column structure configuration',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_table_user` (`table_name`,`user_id`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_data_source_id` (`data_source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Column preferences for data tables';

COMMIT;
