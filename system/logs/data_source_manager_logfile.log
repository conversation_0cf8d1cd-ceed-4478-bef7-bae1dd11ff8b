[data_source_manager] [2025-08-14 13:10:08] [data_source_manager.class.php:264] Created data source: Data-Table with Data Source - autobooks_import_bluebeam_data with ID: 45
[data_source_manager] [2025-08-14 13:10:08] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-14 13:45:39] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-14 13:45:39] [data_source_manager.class.php:314] Generated alias: serial_number AS subscription_reference
[data_source_manager] [2025-08-14 13:45:39] [data_source_manager.class.php:314] Generated alias: name AS company_name
[data_source_manager] [2025-08-14 13:45:39] [data_source_manager.class.php:314] Generated alias: product_name AS company_name
[data_source_manager] [2025-08-14 13:45:39] [data_source_manager.class.php:314] Generated alias: account_primary_reseller_name AS company_name
[data_source_manager] [2025-08-14 13:45:39] [data_source_manager.class.php:184] Generated 4 column aliases
[data_source_manager] [2025-08-14 13:45:39] [data_source_manager.class.php:264] Created data source: CSV Import: Import_bluebeam with ID: 46
[data_source_manager] [2025-08-14 13:45:47] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-14 13:46:41] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-14 13:47:03] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 07:44:53] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 07:52:48] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 07:53:15] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 08:06:22] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 08:17:16] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-15 08:17:16] [data_source_manager.class.php:314] Generated alias: serial_number AS subscription_reference
[data_source_manager] [2025-08-15 08:17:16] [data_source_manager.class.php:314] Generated alias: name AS company_name
[data_source_manager] [2025-08-15 08:17:16] [data_source_manager.class.php:314] Generated alias: product_name AS company_name
[data_source_manager] [2025-08-15 08:17:16] [data_source_manager.class.php:314] Generated alias: account_primary_reseller_name AS company_name
[data_source_manager] [2025-08-15 08:17:16] [data_source_manager.class.php:184] Generated 4 column aliases
[data_source_manager] [2025-08-15 08:17:16] [data_source_manager.class.php:264] Created data source: CSV Import: Import_bluebeam with ID: 47
[data_source_manager] [2025-08-15 08:17:27] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 08:56:32] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-15 08:56:32] [data_source_manager.class.php:314] Generated alias: serial_number AS subscription_reference
[data_source_manager] [2025-08-15 08:56:32] [data_source_manager.class.php:314] Generated alias: name AS company_name
[data_source_manager] [2025-08-15 08:56:32] [data_source_manager.class.php:314] Generated alias: product_name AS company_name
[data_source_manager] [2025-08-15 08:56:32] [data_source_manager.class.php:314] Generated alias: account_primary_reseller_name AS company_name
[data_source_manager] [2025-08-15 08:56:32] [data_source_manager.class.php:184] Generated 4 column aliases
[data_source_manager] [2025-08-15 08:56:32] [data_source_manager.class.php:264] Created data source: CSV Import: Import_bluebeam with ID: 48
[data_source_manager] [2025-08-15 08:56:49] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 09:02:14] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 09:05:05] [data_source_manager.class.php:575] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 09:46:42] [data_source_manager.class.php:573] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 09:48:12] [data_source_manager.class.php:573] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 09:50:43] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-15 09:50:43] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.serial_number => serial_number_AS_subscription_reference
[data_source_manager] [2025-08-15 09:50:43] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.name => name_AS_company_name
[data_source_manager] [2025-08-15 09:50:43] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.product_name => product_name_AS_company_name
[data_source_manager] [2025-08-15 09:50:43] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.account_primary_reseller_name => account_primary_reseller_name_AS_company_name
[data_source_manager] [2025-08-15 09:50:43] [data_source_manager.class.php:180] Generated 4 column aliases: {"autobooks_import_bluebeam_data.serial_number":"serial_number_AS_subscription_reference","autobooks_import_bluebeam_data.name":"name_AS_company_name","autobooks_import_bluebeam_data.product_name":"product_name_AS_company_name","autobooks_import_bluebeam_data.account_primary_reseller_name":"account_primary_reseller_name_AS_company_name"}
[data_source_manager] [2025-08-15 09:50:43] [data_source_manager.class.php:260] Created data source: CSV Import: Import_bluebeam with ID: 49
[data_source_manager] [2025-08-15 10:20:28] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-15 10:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.serial_number => serial_number_AS_subscription_reference
[data_source_manager] [2025-08-15 10:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.name => name_AS_company_name
[data_source_manager] [2025-08-15 10:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.product_name => product_name_AS_company_name
[data_source_manager] [2025-08-15 10:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.account_primary_reseller_name => account_primary_reseller_name_AS_company_name
[data_source_manager] [2025-08-15 10:20:28] [data_source_manager.class.php:180] Generated 4 column aliases: {"autobooks_import_bluebeam_data.serial_number":"serial_number_AS_subscription_reference","autobooks_import_bluebeam_data.name":"name_AS_company_name","autobooks_import_bluebeam_data.product_name":"product_name_AS_company_name","autobooks_import_bluebeam_data.account_primary_reseller_name":"account_primary_reseller_name_AS_company_name"}
[data_source_manager] [2025-08-15 10:20:28] [data_source_manager.class.php:260] Created data source: CSV Import: Import_bluebeam with ID: 50
[data_source_manager] [2025-08-15 12:17:45] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `serial_number_AS_subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `name_AS_company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name_AS_company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `account_primary_reseller_name_AS_company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-15 13:47:58] [data_source_manager.class.php:260] Created data source: tstAutodesk Subscriptions with ID: 51
[data_source_manager] [2025-08-16 08:42:16] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `serial_number_AS_subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `name_AS_company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name_AS_company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `account_primary_reseller_name_AS_company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 08:44:45] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-16 08:44:45] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.serial_number => serial_number_AS_subscription_reference
[data_source_manager] [2025-08-16 08:44:45] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.name => name_AS_company_name
[data_source_manager] [2025-08-16 08:44:45] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.product_name => product_name_AS_company_name
[data_source_manager] [2025-08-16 08:44:45] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.account_primary_reseller_name => account_primary_reseller_name_AS_company_name
[data_source_manager] [2025-08-16 08:44:45] [data_source_manager.class.php:180] Generated 4 column aliases: {"autobooks_import_bluebeam_data.serial_number":"serial_number_AS_subscription_reference","autobooks_import_bluebeam_data.name":"name_AS_company_name","autobooks_import_bluebeam_data.product_name":"product_name_AS_company_name","autobooks_import_bluebeam_data.account_primary_reseller_name":"account_primary_reseller_name_AS_company_name"}
[data_source_manager] [2025-08-16 08:44:45] [data_source_manager.class.php:260] Created data source: CSV Import: Import_bluebeam with ID: 52
[data_source_manager] [2025-08-16 08:45:12] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `serial_number_AS_subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `name_AS_company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name_AS_company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `account_primary_reseller_name_AS_company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 08:46:00] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-16 08:46:00] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.serial_number => subscription_reference
[data_source_manager] [2025-08-16 08:46:00] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.name => company_name
[data_source_manager] [2025-08-16 08:46:00] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.product_name => company_name
[data_source_manager] [2025-08-16 08:46:00] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.account_primary_reseller_name => company_name
[data_source_manager] [2025-08-16 08:46:00] [data_source_manager.class.php:180] Generated 4 column aliases: {"autobooks_import_bluebeam_data.serial_number":"subscription_reference","autobooks_import_bluebeam_data.name":"company_name","autobooks_import_bluebeam_data.product_name":"company_name","autobooks_import_bluebeam_data.account_primary_reseller_name":"company_name"}
[data_source_manager] [2025-08-16 08:46:00] [data_source_manager.class.php:260] Created data source: CSV Import: Import_bluebeam with ID: 53
[data_source_manager] [2025-08-16 08:46:05] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 08:48:45] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 08:54:14] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 08:54:33] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 08:54:51] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 08:54:59] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 09:01:38] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 10:30:14] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 11:53:46] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `company_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `company_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `autobooks_import_bluebeam_data_order_shipping_address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 11:54:09] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-16 11:54:09] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.serial_number => subscription_reference
[data_source_manager] [2025-08-16 11:54:09] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.name => company_name
[data_source_manager] [2025-08-16 11:54:09] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.product_name => product_name
[data_source_manager] [2025-08-16 11:54:09] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.account_primary_reseller_name => reseller_name
[data_source_manager] [2025-08-16 11:54:09] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_address => address
[data_source_manager] [2025-08-16 11:54:09] [data_source_manager.class.php:180] Generated 5 column aliases: {"autobooks_import_bluebeam_data.serial_number":"subscription_reference","autobooks_import_bluebeam_data.name":"company_name","autobooks_import_bluebeam_data.product_name":"product_name","autobooks_import_bluebeam_data.account_primary_reseller_name":"reseller_name","autobooks_import_bluebeam_data.order_shipping_address":"address"}
[data_source_manager] [2025-08-16 11:54:09] [data_source_manager.class.php:260] Created data source: CSV Import: Import_bluebeam with ID: 54
[data_source_manager] [2025-08-16 11:54:13] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 17:23:05] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 17:37:04] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `autobooks_import_bluebeam_data_order_shipping_city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `autobooks_import_bluebeam_data_order_shipping_state_province`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `autobooks_import_bluebeam_data_order_shipping_country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.serial_number => subscription_reference
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.name => company_name
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.product_name => product_name
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.account_primary_reseller_name => reseller_name
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_address => address
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_city => city
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_state_province => state
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_country => country
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:180] Generated 8 column aliases: {"autobooks_import_bluebeam_data.serial_number":"subscription_reference","autobooks_import_bluebeam_data.name":"company_name","autobooks_import_bluebeam_data.product_name":"product_name","autobooks_import_bluebeam_data.account_primary_reseller_name":"reseller_name","autobooks_import_bluebeam_data.order_shipping_address":"address","autobooks_import_bluebeam_data.order_shipping_city":"city","autobooks_import_bluebeam_data.order_shipping_state_province":"state","autobooks_import_bluebeam_data.order_shipping_country":"country"}
[data_source_manager] [2025-08-16 17:38:23] [data_source_manager.class.php:260] Created data source: CSV Import: Import_bluebeam with ID: 55
[data_source_manager] [2025-08-16 17:38:25] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 17:38:29] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `autobooks_import_bluebeam_data_quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `autobooks_import_bluebeam_data_end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.serial_number => subscription_reference
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.name => company_name
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.product_name => product_name
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.quantity => quantity
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.end_date => end_date
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.account_primary_reseller_name => reseller_name
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_address => address
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_city => city
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_state_province => state
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_country => country
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:180] Generated 10 column aliases: {"autobooks_import_bluebeam_data.serial_number":"subscription_reference","autobooks_import_bluebeam_data.name":"company_name","autobooks_import_bluebeam_data.product_name":"product_name","autobooks_import_bluebeam_data.quantity":"quantity","autobooks_import_bluebeam_data.end_date":"end_date","autobooks_import_bluebeam_data.account_primary_reseller_name":"reseller_name","autobooks_import_bluebeam_data.order_shipping_address":"address","autobooks_import_bluebeam_data.order_shipping_city":"city","autobooks_import_bluebeam_data.order_shipping_state_province":"state","autobooks_import_bluebeam_data.order_shipping_country":"country"}
[data_source_manager] [2025-08-16 18:38:20] [data_source_manager.class.php:260] Created data source: CSV Import: Import_bluebeam with ID: 56
[data_source_manager] [2025-08-16 18:38:28] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 18:39:10] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 18:39:19] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 18:39:38] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 18:44:16] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 21:00:01] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-16 21:00:51] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 21:05:53] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-16 21:08:44] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-16 21:09:42] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-16 21:13:12] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 21:13:16] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-16 21:13:16] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 21:14:35] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 22:30:36] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 22:30:55] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 22:31:00] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-16 23:26:18] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-16 23:26:25] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 23:26:55] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 23:27:01] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-16 23:27:24] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-16 23:28:16] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 00:25:01] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 00:26:18] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 00:35:07] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 00:35:09] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 00:35:12] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 00:35:37] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 00:35:45] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 00:35:53] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 00:38:44] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 00:38:53] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 00:40:45] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 00:40:49] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 00:41:07] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 00:41:25] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 00:41:48] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 14:49:02] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 14:49:09] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 14:49:21] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 18:49:09] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 18:51:11] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 18:51:27] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 18:52:59] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 18:53:17] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 18:53:21] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 18:53:29] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 19:03:07] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `autobooks_import_bluebeam_data_contract`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `autobooks_import_bluebeam_data_order_po_number`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 19:04:19] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 19:04:51] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 19:44:12] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 19:56:56] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.serial_number => subscription_reference
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.name => company_name
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.contract => subscription_reference
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.product_name => product_name
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.quantity => quantity
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.end_date => end_date
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.account_primary_reseller_name => reseller_name
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_po_number => purchase_order
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_address => address
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_city => city
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_state_province => state
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_country => country
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:180] Generated 12 column aliases: {"autobooks_import_bluebeam_data.serial_number":"subscription_reference","autobooks_import_bluebeam_data.name":"company_name","autobooks_import_bluebeam_data.contract":"subscription_reference","autobooks_import_bluebeam_data.product_name":"product_name","autobooks_import_bluebeam_data.quantity":"quantity","autobooks_import_bluebeam_data.end_date":"end_date","autobooks_import_bluebeam_data.account_primary_reseller_name":"reseller_name","autobooks_import_bluebeam_data.order_po_number":"purchase_order","autobooks_import_bluebeam_data.order_shipping_address":"address","autobooks_import_bluebeam_data.order_shipping_city":"city","autobooks_import_bluebeam_data.order_shipping_state_province":"state","autobooks_import_bluebeam_data.order_shipping_country":"country"}
[data_source_manager] [2025-08-17 19:57:24] [data_source_manager.class.php:260] Created data source: CSV Import: Import_bluebeam with ID: 57
[data_source_manager] [2025-08-17 20:16:31] [data_source_manager.class.php:573] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.serial_number => subscription_reference
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.name => company_name
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.contract => subscription_reference
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.product_name => product_name
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.quantity => quantity
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.end_date => end_date
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.account_primary_reseller_name => reseller_name
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_po_number => purchase_order
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_address => address
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_city => city
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_state_province => state
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:312] Generated alias: autobooks_import_bluebeam_data.order_shipping_country => country
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:180] Generated 12 column aliases: {"autobooks_import_bluebeam_data.serial_number":"subscription_reference","autobooks_import_bluebeam_data.name":"company_name","autobooks_import_bluebeam_data.contract":"subscription_reference","autobooks_import_bluebeam_data.product_name":"product_name","autobooks_import_bluebeam_data.quantity":"quantity","autobooks_import_bluebeam_data.end_date":"end_date","autobooks_import_bluebeam_data.account_primary_reseller_name":"reseller_name","autobooks_import_bluebeam_data.order_po_number":"purchase_order","autobooks_import_bluebeam_data.order_shipping_address":"address","autobooks_import_bluebeam_data.order_shipping_city":"city","autobooks_import_bluebeam_data.order_shipping_state_province":"state","autobooks_import_bluebeam_data.order_shipping_country":"country"}
[data_source_manager] [2025-08-17 20:20:01] [data_source_manager.class.php:260] Created data source: CSV Import: Import_bluebeam with ID: 58
[data_source_manager] [2025-08-17 20:20:07] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `purchase_order`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.sold_to_name => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.vendor_name => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.reseller_number => reseller_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.reseller_vendor_id => reseller_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_vendor_id => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_name => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_address_1 => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_address_2 => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_address_3 => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_city => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_state => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_zip_code => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_country => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_account_type => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_contact_name => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_contact_email => email
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_contact_phone => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.end_customer_industry_segment => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.agreement_program_name => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.agreement_number => subscription_reference
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.agreement_start_date => start_date
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.agreement_end_date => end_date
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.agreement_status => status
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_name => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_family => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_market_segment => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_release => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_type => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_deployment => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_sku => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_sku_description => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_part => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_list_price => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.product_list_price_currency => product_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.subscription_id => subscription_reference
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.subscription_serial_number => subscription_reference
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.subscription_status => status
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.subscription_quantity => quantity
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.subscription_start_date => start_date
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.subscription_end_date => end_date
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.subscription_contact_name => company_name
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.subscription_contact_email => email
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.quotation_status => status
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:312] Generated alias: autobooks_import_sketchup_data.quotation_due_date => end_date
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:180] Generated 44 column aliases: {"autobooks_import_sketchup_data.sold_to_name":"company_name","autobooks_import_sketchup_data.vendor_name":"company_name","autobooks_import_sketchup_data.reseller_number":"reseller_name","autobooks_import_sketchup_data.reseller_vendor_id":"reseller_name","autobooks_import_sketchup_data.end_customer_vendor_id":"company_name","autobooks_import_sketchup_data.end_customer_name":"company_name","autobooks_import_sketchup_data.end_customer_address_1":"company_name","autobooks_import_sketchup_data.end_customer_address_2":"company_name","autobooks_import_sketchup_data.end_customer_address_3":"company_name","autobooks_import_sketchup_data.end_customer_city":"company_name","autobooks_import_sketchup_data.end_customer_state":"company_name","autobooks_import_sketchup_data.end_customer_zip_code":"company_name","autobooks_import_sketchup_data.end_customer_country":"company_name","autobooks_import_sketchup_data.end_customer_account_type":"company_name","autobooks_import_sketchup_data.end_customer_contact_name":"company_name","autobooks_import_sketchup_data.end_customer_contact_email":"email","autobooks_import_sketchup_data.end_customer_contact_phone":"company_name","autobooks_import_sketchup_data.end_customer_industry_segment":"company_name","autobooks_import_sketchup_data.agreement_program_name":"product_name","autobooks_import_sketchup_data.agreement_number":"subscription_reference","autobooks_import_sketchup_data.agreement_start_date":"start_date","autobooks_import_sketchup_data.agreement_end_date":"end_date","autobooks_import_sketchup_data.agreement_status":"status","autobooks_import_sketchup_data.product_name":"product_name","autobooks_import_sketchup_data.product_family":"product_name","autobooks_import_sketchup_data.product_market_segment":"product_name","autobooks_import_sketchup_data.product_release":"product_name","autobooks_import_sketchup_data.product_type":"product_name","autobooks_import_sketchup_data.product_deployment":"product_name","autobooks_import_sketchup_data.product_sku":"product_name","autobooks_import_sketchup_data.product_sku_description":"product_name","autobooks_import_sketchup_data.product_part":"product_name","autobooks_import_sketchup_data.product_list_price":"product_name","autobooks_import_sketchup_data.product_list_price_currency":"product_name","autobooks_import_sketchup_data.subscription_id":"subscription_reference","autobooks_import_sketchup_data.subscription_serial_number":"subscription_reference","autobooks_import_sketchup_data.subscription_status":"status","autobooks_import_sketchup_data.subscription_quantity":"quantity","autobooks_import_sketchup_data.subscription_start_date":"start_date","autobooks_import_sketchup_data.subscription_end_date":"end_date","autobooks_import_sketchup_data.subscription_contact_name":"company_name","autobooks_import_sketchup_data.subscription_contact_email":"email","autobooks_import_sketchup_data.quotation_status":"status","autobooks_import_sketchup_data.quotation_due_date":"end_date"}
[data_source_manager] [2025-08-17 20:20:28] [data_source_manager.class.php:260] Created data source: CSV Import: Import_sketchup with ID: 59
[data_source_manager] [2025-08-17 20:20:31] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `purchase_order`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 20:20:34] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_sketchup_data`.`sold_to_name` AS `company_name`, `autobooks_import_sketchup_data`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number`, `autobooks_import_sketchup_data`.`vendor_name` AS `company_name`, `autobooks_import_sketchup_data`.`reseller_number` AS `reseller_name`, `autobooks_import_sketchup_data`.`reseller_vendor_id` AS `reseller_name`, `autobooks_import_sketchup_data`.`end_customer_vendor_id` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_name` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_1` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_2` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_3` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_city` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_state` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_zip_code` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_country` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_account_type` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_contact_name` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_contact_email` AS `email`, `autobooks_import_sketchup_data`.`end_customer_contact_phone` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_industry_segment` AS `company_name`, `autobooks_import_sketchup_data`.`agreement_program_name` AS `product_name`, `autobooks_import_sketchup_data`.`agreement_number` AS `subscription_reference`, `autobooks_import_sketchup_data`.`agreement_start_date` AS `start_date`, `autobooks_import_sketchup_data`.`agreement_end_date` AS `end_date`, `autobooks_import_sketchup_data`.`agreement_terms` AS `autobooks_import_sketchup_data_agreement_terms`, `autobooks_import_sketchup_data`.`agreement_type` AS `autobooks_import_sketchup_data_agreement_type`, `autobooks_import_sketchup_data`.`agreement_status` AS `status`, `autobooks_import_sketchup_data`.`agreement_support_level` AS `autobooks_import_sketchup_data_agreement_support_level`, `autobooks_import_sketchup_data`.`agreement_days_due` AS `autobooks_import_sketchup_data_agreement_days_due`, `autobooks_import_sketchup_data`.`agreement_autorenew` AS `autobooks_import_sketchup_data_agreement_autorenew`, `autobooks_import_sketchup_data`.`product_name` AS `product_name`, `autobooks_import_sketchup_data`.`product_family` AS `product_name`, `autobooks_import_sketchup_data`.`product_market_segment` AS `product_name`, `autobooks_import_sketchup_data`.`product_release` AS `product_name`, `autobooks_import_sketchup_data`.`product_type` AS `product_name`, `autobooks_import_sketchup_data`.`product_deployment` AS `product_name`, `autobooks_import_sketchup_data`.`product_sku` AS `product_name`, `autobooks_import_sketchup_data`.`product_sku_description` AS `product_name`, `autobooks_import_sketchup_data`.`product_part` AS `product_name`, `autobooks_import_sketchup_data`.`product_list_price` AS `product_name`, `autobooks_import_sketchup_data`.`product_list_price_currency` AS `product_name`, `autobooks_import_sketchup_data`.`subscription_id` AS `subscription_reference`, `autobooks_import_sketchup_data`.`subscription_serial_number` AS `subscription_reference`, `autobooks_import_sketchup_data`.`subscription_status` AS `status`, `autobooks_import_sketchup_data`.`subscription_quantity` AS `quantity`, `autobooks_import_sketchup_data`.`subscription_start_date` AS `start_date`, `autobooks_import_sketchup_data`.`subscription_end_date` AS `end_date`, `autobooks_import_sketchup_data`.`subscription_contact_name` AS `company_name`, `autobooks_import_sketchup_data`.`subscription_contact_email` AS `email`, `autobooks_import_sketchup_data`.`subscription_level` AS `autobooks_import_sketchup_data_subscription_level`, `autobooks_import_sketchup_data`.`subscription_days_due` AS `autobooks_import_sketchup_data_subscription_days_due`, `autobooks_import_sketchup_data`.`quotation_id` AS `autobooks_import_sketchup_data_quotation_id`, `autobooks_import_sketchup_data`.`quotation_type` AS `autobooks_import_sketchup_data_quotation_type`, `autobooks_import_sketchup_data`.`quotation_vendor_id` AS `autobooks_import_sketchup_data_quotation_vendor_id`, `autobooks_import_sketchup_data`.`quotation_deal_registration_number` AS `autobooks_import_sketchup_data_quotation_deal_registration_number`, `autobooks_import_sketchup_data`.`quotation_status` AS `status`, `autobooks_import_sketchup_data`.`quotation_resellerpo_previous` AS `autobooks_import_sketchup_data_quotation_resellerpo_previous`, `autobooks_import_sketchup_data`.`quotation_due_date` AS `end_date`, `autobooks_import_sketchup_data`.`flaer_phase` AS `autobooks_import_sketchup_data_flaer_phase`, `autobooks_import_sketchup_data`.`updated` AS `autobooks_import_sketchup_data_updated` FROM `autobooks_import_sketchup_data` LIMIT 50
[data_source_manager] [2025-08-17 20:21:24] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `purchase_order`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 20:21:29] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_sketchup_data`.`sold_to_name` AS `company_name`, `autobooks_import_sketchup_data`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number`, `autobooks_import_sketchup_data`.`vendor_name` AS `company_name`, `autobooks_import_sketchup_data`.`reseller_number` AS `reseller_name`, `autobooks_import_sketchup_data`.`reseller_vendor_id` AS `reseller_name`, `autobooks_import_sketchup_data`.`end_customer_vendor_id` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_name` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_1` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_2` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_3` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_city` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_state` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_zip_code` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_country` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_account_type` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_contact_name` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_contact_email` AS `email`, `autobooks_import_sketchup_data`.`end_customer_contact_phone` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_industry_segment` AS `company_name`, `autobooks_import_sketchup_data`.`agreement_program_name` AS `product_name`, `autobooks_import_sketchup_data`.`agreement_number` AS `subscription_reference`, `autobooks_import_sketchup_data`.`agreement_start_date` AS `start_date`, `autobooks_import_sketchup_data`.`agreement_end_date` AS `end_date`, `autobooks_import_sketchup_data`.`agreement_terms` AS `autobooks_import_sketchup_data_agreement_terms`, `autobooks_import_sketchup_data`.`agreement_type` AS `autobooks_import_sketchup_data_agreement_type`, `autobooks_import_sketchup_data`.`agreement_status` AS `status`, `autobooks_import_sketchup_data`.`agreement_support_level` AS `autobooks_import_sketchup_data_agreement_support_level`, `autobooks_import_sketchup_data`.`agreement_days_due` AS `autobooks_import_sketchup_data_agreement_days_due`, `autobooks_import_sketchup_data`.`agreement_autorenew` AS `autobooks_import_sketchup_data_agreement_autorenew`, `autobooks_import_sketchup_data`.`product_name` AS `product_name`, `autobooks_import_sketchup_data`.`product_family` AS `product_name`, `autobooks_import_sketchup_data`.`product_market_segment` AS `product_name`, `autobooks_import_sketchup_data`.`product_release` AS `product_name`, `autobooks_import_sketchup_data`.`product_type` AS `product_name`, `autobooks_import_sketchup_data`.`product_deployment` AS `product_name`, `autobooks_import_sketchup_data`.`product_sku` AS `product_name`, `autobooks_import_sketchup_data`.`product_sku_description` AS `product_name`, `autobooks_import_sketchup_data`.`product_part` AS `product_name`, `autobooks_import_sketchup_data`.`product_list_price` AS `product_name`, `autobooks_import_sketchup_data`.`product_list_price_currency` AS `product_name`, `autobooks_import_sketchup_data`.`subscription_id` AS `subscription_reference`, `autobooks_import_sketchup_data`.`subscription_serial_number` AS `subscription_reference`, `autobooks_import_sketchup_data`.`subscription_status` AS `status`, `autobooks_import_sketchup_data`.`subscription_quantity` AS `quantity`, `autobooks_import_sketchup_data`.`subscription_start_date` AS `start_date`, `autobooks_import_sketchup_data`.`subscription_end_date` AS `end_date`, `autobooks_import_sketchup_data`.`subscription_contact_name` AS `company_name`, `autobooks_import_sketchup_data`.`subscription_contact_email` AS `email`, `autobooks_import_sketchup_data`.`subscription_level` AS `autobooks_import_sketchup_data_subscription_level`, `autobooks_import_sketchup_data`.`subscription_days_due` AS `autobooks_import_sketchup_data_subscription_days_due`, `autobooks_import_sketchup_data`.`quotation_id` AS `autobooks_import_sketchup_data_quotation_id`, `autobooks_import_sketchup_data`.`quotation_type` AS `autobooks_import_sketchup_data_quotation_type`, `autobooks_import_sketchup_data`.`quotation_vendor_id` AS `autobooks_import_sketchup_data_quotation_vendor_id`, `autobooks_import_sketchup_data`.`quotation_deal_registration_number` AS `autobooks_import_sketchup_data_quotation_deal_registration_number`, `autobooks_import_sketchup_data`.`quotation_status` AS `status`, `autobooks_import_sketchup_data`.`quotation_resellerpo_previous` AS `autobooks_import_sketchup_data_quotation_resellerpo_previous`, `autobooks_import_sketchup_data`.`quotation_due_date` AS `end_date`, `autobooks_import_sketchup_data`.`flaer_phase` AS `autobooks_import_sketchup_data_flaer_phase`, `autobooks_import_sketchup_data`.`updated` AS `autobooks_import_sketchup_data_updated` FROM `autobooks_import_sketchup_data` LIMIT 50
[data_source_manager] [2025-08-17 20:22:45] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_sketchup_data`.`sold_to_name` AS `company_name`, `autobooks_import_sketchup_data`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number`, `autobooks_import_sketchup_data`.`vendor_name` AS `company_name`, `autobooks_import_sketchup_data`.`reseller_number` AS `reseller_name`, `autobooks_import_sketchup_data`.`reseller_vendor_id` AS `reseller_name`, `autobooks_import_sketchup_data`.`end_customer_vendor_id` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_name` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_1` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_2` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_3` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_city` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_state` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_zip_code` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_country` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_account_type` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_contact_name` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_contact_email` AS `email`, `autobooks_import_sketchup_data`.`end_customer_contact_phone` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_industry_segment` AS `company_name`, `autobooks_import_sketchup_data`.`agreement_program_name` AS `product_name`, `autobooks_import_sketchup_data`.`agreement_number` AS `subscription_reference`, `autobooks_import_sketchup_data`.`agreement_start_date` AS `start_date`, `autobooks_import_sketchup_data`.`agreement_end_date` AS `end_date`, `autobooks_import_sketchup_data`.`agreement_terms` AS `autobooks_import_sketchup_data_agreement_terms`, `autobooks_import_sketchup_data`.`agreement_type` AS `autobooks_import_sketchup_data_agreement_type`, `autobooks_import_sketchup_data`.`agreement_status` AS `status`, `autobooks_import_sketchup_data`.`agreement_support_level` AS `autobooks_import_sketchup_data_agreement_support_level`, `autobooks_import_sketchup_data`.`agreement_days_due` AS `autobooks_import_sketchup_data_agreement_days_due`, `autobooks_import_sketchup_data`.`agreement_autorenew` AS `autobooks_import_sketchup_data_agreement_autorenew`, `autobooks_import_sketchup_data`.`product_name` AS `product_name`, `autobooks_import_sketchup_data`.`product_family` AS `product_name`, `autobooks_import_sketchup_data`.`product_market_segment` AS `product_name`, `autobooks_import_sketchup_data`.`product_release` AS `product_name`, `autobooks_import_sketchup_data`.`product_type` AS `product_name`, `autobooks_import_sketchup_data`.`product_deployment` AS `product_name`, `autobooks_import_sketchup_data`.`product_sku` AS `product_name`, `autobooks_import_sketchup_data`.`product_sku_description` AS `product_name`, `autobooks_import_sketchup_data`.`product_part` AS `product_name`, `autobooks_import_sketchup_data`.`product_list_price` AS `product_name`, `autobooks_import_sketchup_data`.`product_list_price_currency` AS `product_name`, `autobooks_import_sketchup_data`.`subscription_id` AS `subscription_reference`, `autobooks_import_sketchup_data`.`subscription_serial_number` AS `subscription_reference`, `autobooks_import_sketchup_data`.`subscription_status` AS `status`, `autobooks_import_sketchup_data`.`subscription_quantity` AS `quantity`, `autobooks_import_sketchup_data`.`subscription_start_date` AS `start_date`, `autobooks_import_sketchup_data`.`subscription_end_date` AS `end_date`, `autobooks_import_sketchup_data`.`subscription_contact_name` AS `company_name`, `autobooks_import_sketchup_data`.`subscription_contact_email` AS `email`, `autobooks_import_sketchup_data`.`subscription_level` AS `autobooks_import_sketchup_data_subscription_level`, `autobooks_import_sketchup_data`.`subscription_days_due` AS `autobooks_import_sketchup_data_subscription_days_due`, `autobooks_import_sketchup_data`.`quotation_id` AS `autobooks_import_sketchup_data_quotation_id`, `autobooks_import_sketchup_data`.`quotation_type` AS `autobooks_import_sketchup_data_quotation_type`, `autobooks_import_sketchup_data`.`quotation_vendor_id` AS `autobooks_import_sketchup_data_quotation_vendor_id`, `autobooks_import_sketchup_data`.`quotation_deal_registration_number` AS `autobooks_import_sketchup_data_quotation_deal_registration_number`, `autobooks_import_sketchup_data`.`quotation_status` AS `status`, `autobooks_import_sketchup_data`.`quotation_resellerpo_previous` AS `autobooks_import_sketchup_data_quotation_resellerpo_previous`, `autobooks_import_sketchup_data`.`quotation_due_date` AS `end_date`, `autobooks_import_sketchup_data`.`flaer_phase` AS `autobooks_import_sketchup_data_flaer_phase`, `autobooks_import_sketchup_data`.`updated` AS `autobooks_import_sketchup_data_updated` FROM `autobooks_import_sketchup_data` ORDER BY `autobooks_import_sketchup_data_updated` ASC
[data_source_manager] [2025-08-17 20:23:21] [data_source_manager.class.php:573] Executing query: SELECT `autobooks_import_sketchup_data`.`sold_to_name` AS `company_name`, `autobooks_import_sketchup_data`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number`, `autobooks_import_sketchup_data`.`vendor_name` AS `company_name`, `autobooks_import_sketchup_data`.`reseller_number` AS `reseller_name`, `autobooks_import_sketchup_data`.`reseller_vendor_id` AS `reseller_name`, `autobooks_import_sketchup_data`.`end_customer_vendor_id` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_name` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_1` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_2` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_3` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_city` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_state` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_zip_code` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_country` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_account_type` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_contact_name` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_contact_email` AS `email`, `autobooks_import_sketchup_data`.`end_customer_contact_phone` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_industry_segment` AS `company_name`, `autobooks_import_sketchup_data`.`agreement_program_name` AS `product_name`, `autobooks_import_sketchup_data`.`agreement_number` AS `subscription_reference`, `autobooks_import_sketchup_data`.`agreement_start_date` AS `start_date`, `autobooks_import_sketchup_data`.`agreement_end_date` AS `end_date`, `autobooks_import_sketchup_data`.`agreement_terms` AS `autobooks_import_sketchup_data_agreement_terms`, `autobooks_import_sketchup_data`.`agreement_type` AS `autobooks_import_sketchup_data_agreement_type`, `autobooks_import_sketchup_data`.`agreement_status` AS `status`, `autobooks_import_sketchup_data`.`agreement_support_level` AS `autobooks_import_sketchup_data_agreement_support_level`, `autobooks_import_sketchup_data`.`agreement_days_due` AS `autobooks_import_sketchup_data_agreement_days_due`, `autobooks_import_sketchup_data`.`agreement_autorenew` AS `autobooks_import_sketchup_data_agreement_autorenew`, `autobooks_import_sketchup_data`.`product_name` AS `product_name`, `autobooks_import_sketchup_data`.`product_family` AS `product_name`, `autobooks_import_sketchup_data`.`product_market_segment` AS `product_name`, `autobooks_import_sketchup_data`.`product_release` AS `product_name`, `autobooks_import_sketchup_data`.`product_type` AS `product_name`, `autobooks_import_sketchup_data`.`product_deployment` AS `product_name`, `autobooks_import_sketchup_data`.`product_sku` AS `product_name`, `autobooks_import_sketchup_data`.`product_sku_description` AS `product_name`, `autobooks_import_sketchup_data`.`product_part` AS `product_name`, `autobooks_import_sketchup_data`.`product_list_price` AS `product_name`, `autobooks_import_sketchup_data`.`product_list_price_currency` AS `product_name`, `autobooks_import_sketchup_data`.`subscription_id` AS `subscription_reference`, `autobooks_import_sketchup_data`.`subscription_serial_number` AS `subscription_reference`, `autobooks_import_sketchup_data`.`subscription_status` AS `status`, `autobooks_import_sketchup_data`.`subscription_quantity` AS `quantity`, `autobooks_import_sketchup_data`.`subscription_start_date` AS `start_date`, `autobooks_import_sketchup_data`.`subscription_end_date` AS `end_date`, `autobooks_import_sketchup_data`.`subscription_contact_name` AS `company_name`, `autobooks_import_sketchup_data`.`subscription_contact_email` AS `email`, `autobooks_import_sketchup_data`.`subscription_level` AS `autobooks_import_sketchup_data_subscription_level`, `autobooks_import_sketchup_data`.`subscription_days_due` AS `autobooks_import_sketchup_data_subscription_days_due`, `autobooks_import_sketchup_data`.`quotation_id` AS `autobooks_import_sketchup_data_quotation_id`, `autobooks_import_sketchup_data`.`quotation_type` AS `autobooks_import_sketchup_data_quotation_type`, `autobooks_import_sketchup_data`.`quotation_vendor_id` AS `autobooks_import_sketchup_data_quotation_vendor_id`, `autobooks_import_sketchup_data`.`quotation_deal_registration_number` AS `autobooks_import_sketchup_data_quotation_deal_registration_number`, `autobooks_import_sketchup_data`.`quotation_status` AS `status`, `autobooks_import_sketchup_data`.`quotation_resellerpo_previous` AS `autobooks_import_sketchup_data_quotation_resellerpo_previous`, `autobooks_import_sketchup_data`.`quotation_due_date` AS `end_date`, `autobooks_import_sketchup_data`.`flaer_phase` AS `autobooks_import_sketchup_data_flaer_phase`, `autobooks_import_sketchup_data`.`updated` AS `autobooks_import_sketchup_data_updated` FROM `autobooks_import_sketchup_data` LIMIT 50
[data_source_manager] [2025-08-17 20:41:03] [data_source_manager.class.php:599] Executing query: SELECT `autobooks_import_sketchup_data`.`sold_to_name` AS `company_name`, `autobooks_import_sketchup_data`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number`, `autobooks_import_sketchup_data`.`vendor_name` AS `company_name`, `autobooks_import_sketchup_data`.`reseller_number` AS `reseller_name`, `autobooks_import_sketchup_data`.`reseller_vendor_id` AS `reseller_name`, `autobooks_import_sketchup_data`.`end_customer_vendor_id` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_name` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_1` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_2` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_address_3` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_city` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_state` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_zip_code` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_country` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_account_type` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_contact_name` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_contact_email` AS `email`, `autobooks_import_sketchup_data`.`end_customer_contact_phone` AS `company_name`, `autobooks_import_sketchup_data`.`end_customer_industry_segment` AS `company_name`, `autobooks_import_sketchup_data`.`agreement_program_name` AS `product_name`, `autobooks_import_sketchup_data`.`agreement_number` AS `subscription_reference`, `autobooks_import_sketchup_data`.`agreement_start_date` AS `start_date`, `autobooks_import_sketchup_data`.`agreement_end_date` AS `end_date`, `autobooks_import_sketchup_data`.`agreement_terms` AS `autobooks_import_sketchup_data_agreement_terms`, `autobooks_import_sketchup_data`.`agreement_type` AS `autobooks_import_sketchup_data_agreement_type`, `autobooks_import_sketchup_data`.`agreement_status` AS `status`, `autobooks_import_sketchup_data`.`agreement_support_level` AS `autobooks_import_sketchup_data_agreement_support_level`, `autobooks_import_sketchup_data`.`agreement_days_due` AS `autobooks_import_sketchup_data_agreement_days_due`, `autobooks_import_sketchup_data`.`agreement_autorenew` AS `autobooks_import_sketchup_data_agreement_autorenew`, `autobooks_import_sketchup_data`.`product_name` AS `product_name`, `autobooks_import_sketchup_data`.`product_family` AS `product_name`, `autobooks_import_sketchup_data`.`product_market_segment` AS `product_name`, `autobooks_import_sketchup_data`.`product_release` AS `product_name`, `autobooks_import_sketchup_data`.`product_type` AS `product_name`, `autobooks_import_sketchup_data`.`product_deployment` AS `product_name`, `autobooks_import_sketchup_data`.`product_sku` AS `product_name`, `autobooks_import_sketchup_data`.`product_sku_description` AS `product_name`, `autobooks_import_sketchup_data`.`product_part` AS `product_name`, `autobooks_import_sketchup_data`.`product_list_price` AS `product_name`, `autobooks_import_sketchup_data`.`product_list_price_currency` AS `product_name`, `autobooks_import_sketchup_data`.`subscription_id` AS `subscription_reference`, `autobooks_import_sketchup_data`.`subscription_serial_number` AS `subscription_reference`, `autobooks_import_sketchup_data`.`subscription_status` AS `status`, `autobooks_import_sketchup_data`.`subscription_quantity` AS `quantity`, `autobooks_import_sketchup_data`.`subscription_start_date` AS `start_date`, `autobooks_import_sketchup_data`.`subscription_end_date` AS `end_date`, `autobooks_import_sketchup_data`.`subscription_contact_name` AS `company_name`, `autobooks_import_sketchup_data`.`subscription_contact_email` AS `email`, `autobooks_import_sketchup_data`.`subscription_level` AS `autobooks_import_sketchup_data_subscription_level`, `autobooks_import_sketchup_data`.`subscription_days_due` AS `autobooks_import_sketchup_data_subscription_days_due`, `autobooks_import_sketchup_data`.`quotation_id` AS `autobooks_import_sketchup_data_quotation_id`, `autobooks_import_sketchup_data`.`quotation_type` AS `autobooks_import_sketchup_data_quotation_type`, `autobooks_import_sketchup_data`.`quotation_vendor_id` AS `autobooks_import_sketchup_data_quotation_vendor_id`, `autobooks_import_sketchup_data`.`quotation_deal_registration_number` AS `autobooks_import_sketchup_data_quotation_deal_registration_number`, `autobooks_import_sketchup_data`.`quotation_status` AS `status`, `autobooks_import_sketchup_data`.`quotation_resellerpo_previous` AS `autobooks_import_sketchup_data_quotation_resellerpo_previous`, `autobooks_import_sketchup_data`.`quotation_due_date` AS `end_date`, `autobooks_import_sketchup_data`.`flaer_phase` AS `autobooks_import_sketchup_data_flaer_phase`, `autobooks_import_sketchup_data`.`updated` AS `autobooks_import_sketchup_data_updated` FROM `autobooks_import_sketchup_data` LIMIT 50
[data_source_manager] [2025-08-17 20:42:30] [data_source_manager.class.php:599] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `purchase_order`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 20:42:31] [data_source_manager.class.php:599] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `purchase_order`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 20:42:51] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-17 20:42:55] [data_source_manager.class.php:599] Executing query: SELECT `autobooks_import_bluebeam_data`.`serial_number` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`name` AS `company_name`, `autobooks_import_bluebeam_data`.`contract` AS `subscription_reference`, `autobooks_import_bluebeam_data`.`product_name` AS `product_name`, `autobooks_import_bluebeam_data`.`quantity` AS `quantity`, `autobooks_import_bluebeam_data`.`end_date` AS `end_date`, `autobooks_import_bluebeam_data`.`account_primary_reseller_name` AS `reseller_name`, `autobooks_import_bluebeam_data`.`order_po_number` AS `purchase_order`, `autobooks_import_bluebeam_data`.`order_shipping_address` AS `address`, `autobooks_import_bluebeam_data`.`order_shipping_city` AS `city`, `autobooks_import_bluebeam_data`.`order_shipping_state_province` AS `state`, `autobooks_import_bluebeam_data`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-17 20:47:29] [data_source_manager.class.php:599] Executing query: SELECT `bluebeam`.`serial_number` AS `subscription_reference`, `bluebeam`.`name` AS `company_name`, `bluebeam`.`contract` AS `subscription_reference`, `bluebeam`.`product_name` AS `product_name`, `bluebeam`.`quantity` AS `quantity`, `bluebeam`.`end_date` AS `end_date`, `bluebeam`.`account_primary_reseller_name` AS `reseller_name`, `bluebeam`.`order_po_number` AS `purchase_order`, `bluebeam`.`order_shipping_address` AS `address`, `bluebeam`.`order_shipping_city` AS `city`, `bluebeam`.`order_shipping_state_province` AS `state`, `bluebeam`.`order_shipping_country` AS `country`, `bluebeam`.`created_at` AS `autobooks_import_bluebeam_data_created_at`, `bluebeam`.`updated_at` AS `autobooks_import_bluebeam_data_updated_at` FROM `autobooks_import_bluebeam_data` AS `bluebeam` LIMIT 50
[data_source_manager] [2025-08-17 20:53:09] [data_source_manager.class.php:599] Executing query: SELECT `bluebeam`.`serial_number` AS `subscription_reference`, `bluebeam`.`name` AS `company_name`, `bluebeam`.`contract` AS `subscription_reference`, `bluebeam`.`product_name` AS `product_name`, `bluebeam`.`quantity` AS `quantity`, `bluebeam`.`end_date` AS `end_date`, `bluebeam`.`account_primary_reseller_name` AS `reseller_name`, `bluebeam`.`order_po_number` AS `purchase_order`, `bluebeam`.`order_shipping_address` AS `address`, `bluebeam`.`order_shipping_city` AS `city`, `bluebeam`.`order_shipping_state_province` AS `state`, `bluebeam`.`order_shipping_country` AS `country`, `bluebeam`.`created_at` AS `autobooks_import_bluebeam_data_created_at`, `bluebeam`.`updated_at` AS `autobooks_import_bluebeam_data_updated_at` FROM `autobooks_import_bluebeam_data` AS `bluebeam` LIMIT 50
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.serial_number => subscription_reference
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.name => company_name
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:317] Alias conflict: contract -> subscription_reference (score: 85) loses to serial_number (score: 85)
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.product_name => product_name
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.quantity => quantity
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.end_date => end_date
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.account_primary_reseller_name => reseller_name
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.order_po_number => purchase_order
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.order_shipping_address => address
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.order_shipping_city => city
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.order_shipping_state_province => state
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:337] Generated alias: autobooks_import_bluebeam_data.order_shipping_country => country
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:180] Generated 11 column aliases: {"autobooks_import_bluebeam_data.serial_number":"subscription_reference","autobooks_import_bluebeam_data.name":"company_name","autobooks_import_bluebeam_data.product_name":"product_name","autobooks_import_bluebeam_data.quantity":"quantity","autobooks_import_bluebeam_data.end_date":"end_date","autobooks_import_bluebeam_data.account_primary_reseller_name":"reseller_name","autobooks_import_bluebeam_data.order_po_number":"purchase_order","autobooks_import_bluebeam_data.order_shipping_address":"address","autobooks_import_bluebeam_data.order_shipping_city":"city","autobooks_import_bluebeam_data.order_shipping_state_province":"state","autobooks_import_bluebeam_data.order_shipping_country":"country"}
[data_source_manager] [2025-08-17 21:17:57] [data_source_manager.class.php:260] Created data source: CSV Import: Bluebeam with ID: 60
[data_source_manager] [2025-08-17 21:18:03] [data_source_manager.class.php:599] Executing query: SELECT `bluebeam`.`serial_number` AS `subscription_reference`, `bluebeam`.`name` AS `company_name`, `bluebeam`.`contract` AS `autobooks_import_bluebeam_data_contract`, `bluebeam`.`product_name` AS `product_name`, `bluebeam`.`quantity` AS `quantity`, `bluebeam`.`end_date` AS `end_date`, `bluebeam`.`account_primary_reseller_name` AS `reseller_name`, `bluebeam`.`order_po_number` AS `purchase_order`, `bluebeam`.`order_shipping_address` AS `address`, `bluebeam`.`order_shipping_city` AS `city`, `bluebeam`.`order_shipping_state_province` AS `state`, `bluebeam`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` AS `bluebeam` LIMIT 50
[data_source_manager] [2025-08-17 21:20:54] [data_source_manager.class.php:599] Executing query: SELECT `bluebeam`.`serial_number` AS `subscription_reference`, `bluebeam`.`name` AS `company_name`, `bluebeam`.`contract` AS `autobooks_import_bluebeam_data_contract`, `bluebeam`.`product_name` AS `product_name`, `bluebeam`.`quantity` AS `quantity`, `bluebeam`.`end_date` AS `end_date`, `bluebeam`.`account_primary_reseller_name` AS `reseller_name`, `bluebeam`.`order_po_number` AS `purchase_order`, `bluebeam`.`order_shipping_address` AS `address`, `bluebeam`.`order_shipping_city` AS `city`, `bluebeam`.`order_shipping_state_province` AS `state`, `bluebeam`.`order_shipping_country` AS `country` FROM `autobooks_import_bluebeam_data` AS `bluebeam` LIMIT 50
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:337] Generated alias: autobooks_import_sketchup_data.sold_to_name => company_name
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: vendor_name -> company_name (score: 74) loses to sold_to_name (score: 74)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:337] Generated alias: autobooks_import_sketchup_data.reseller_number => reseller_name
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: reseller_vendor_id -> reseller_name (score: 63.3) loses to reseller_number (score: 63.3)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_vendor_id -> company_name (score: 65.8) loses to sold_to_name (score: 74)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:315] Alias conflict: end_customer_name -> company_name (score: 77) replaces sold_to_name (score: 74)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:337] Generated alias: autobooks_import_sketchup_data.end_customer_name => company_name
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_address_1 -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_address_2 -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_address_3 -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_city -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_state -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_zip_code -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_country -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_account_type -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_contact_name -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:337] Generated alias: autobooks_import_sketchup_data.end_customer_contact_email => email
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_contact_phone -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: end_customer_industry_segment -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:337] Generated alias: autobooks_import_sketchup_data.agreement_program_name => product_name
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:337] Generated alias: autobooks_import_sketchup_data.agreement_number => subscription_reference
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:337] Generated alias: autobooks_import_sketchup_data.agreement_start_date => start_date
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:337] Generated alias: autobooks_import_sketchup_data.agreement_end_date => end_date
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:337] Generated alias: autobooks_import_sketchup_data.agreement_status => status
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_name -> product_name (score: 79) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_family -> product_name (score: 79) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_market_segment -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_release -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_type -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_deployment -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_sku -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_sku_description -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_part -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_list_price -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: product_list_price_currency -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: subscription_id -> subscription_reference (score: 85) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: subscription_serial_number -> subscription_reference (score: 79) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: subscription_status -> status (score: 58) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:337] Generated alias: autobooks_import_sketchup_data.subscription_quantity => quantity
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: subscription_start_date -> start_date (score: 58) loses to agreement_start_date (score: 58)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: subscription_end_date -> end_date (score: 58) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: subscription_contact_name -> company_name (score: 63.4) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: subscription_contact_email -> email (score: 82) loses to end_customer_contact_email (score: 82)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: quotation_status -> status (score: 52) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:317] Alias conflict: quotation_due_date -> end_date (score: 52) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:180] Generated 10 column aliases: {"autobooks_import_sketchup_data.sold_to_name":"company_name","autobooks_import_sketchup_data.reseller_number":"reseller_name","autobooks_import_sketchup_data.end_customer_name":"company_name","autobooks_import_sketchup_data.end_customer_contact_email":"email","autobooks_import_sketchup_data.agreement_program_name":"product_name","autobooks_import_sketchup_data.agreement_number":"subscription_reference","autobooks_import_sketchup_data.agreement_start_date":"start_date","autobooks_import_sketchup_data.agreement_end_date":"end_date","autobooks_import_sketchup_data.agreement_status":"status","autobooks_import_sketchup_data.subscription_quantity":"quantity"}
[data_source_manager] [2025-08-17 21:25:08] [data_source_manager.class.php:260] Created data source: CSV Import: Sketchup with ID: 61
[data_source_manager] [2025-08-17 21:25:16] [data_source_manager.class.php:599] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number`, `sketchup`.`vendor_name` AS `autobooks_import_sketchup_data_vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `autobooks_import_sketchup_data_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `autobooks_import_sketchup_data_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `autobooks_import_sketchup_data_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `autobooks_import_sketchup_data_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `autobooks_import_sketchup_data_end_customer_address_3`, `sketchup`.`end_customer_city` AS `autobooks_import_sketchup_data_end_customer_city`, `sketchup`.`end_customer_state` AS `autobooks_import_sketchup_data_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `autobooks_import_sketchup_data_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `autobooks_import_sketchup_data_end_customer_country`, `sketchup`.`end_customer_account_type` AS `autobooks_import_sketchup_data_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `autobooks_import_sketchup_data_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `autobooks_import_sketchup_data_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `autobooks_import_sketchup_data_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `autobooks_import_sketchup_data_agreement_terms`, `sketchup`.`agreement_type` AS `autobooks_import_sketchup_data_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `autobooks_import_sketchup_data_agreement_support_level`, `sketchup`.`agreement_days_due` AS `autobooks_import_sketchup_data_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `autobooks_import_sketchup_data_agreement_autorenew`, `sketchup`.`product_name` AS `autobooks_import_sketchup_data_product_name`, `sketchup`.`product_family` AS `autobooks_import_sketchup_data_product_family`, `sketchup`.`product_market_segment` AS `autobooks_import_sketchup_data_product_market_segment`, `sketchup`.`product_release` AS `autobooks_import_sketchup_data_product_release`, `sketchup`.`product_type` AS `autobooks_import_sketchup_data_product_type`, `sketchup`.`product_deployment` AS `autobooks_import_sketchup_data_product_deployment`, `sketchup`.`product_sku` AS `autobooks_import_sketchup_data_product_sku`, `sketchup`.`product_sku_description` AS `autobooks_import_sketchup_data_product_sku_description`, `sketchup`.`product_part` AS `autobooks_import_sketchup_data_product_part`, `sketchup`.`product_list_price` AS `autobooks_import_sketchup_data_product_list_price`, `sketchup`.`product_list_price_currency` AS `autobooks_import_sketchup_data_product_list_price_currency`, `sketchup`.`subscription_id` AS `autobooks_import_sketchup_data_subscription_id`, `sketchup`.`subscription_serial_number` AS `autobooks_import_sketchup_data_subscription_serial_number`, `sketchup`.`subscription_status` AS `autobooks_import_sketchup_data_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `autobooks_import_sketchup_data_subscription_start_date`, `sketchup`.`subscription_end_date` AS `autobooks_import_sketchup_data_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `autobooks_import_sketchup_data_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `autobooks_import_sketchup_data_subscription_contact_email`, `sketchup`.`subscription_level` AS `autobooks_import_sketchup_data_subscription_level`, `sketchup`.`subscription_days_due` AS `autobooks_import_sketchup_data_subscription_days_due`, `sketchup`.`quotation_id` AS `autobooks_import_sketchup_data_quotation_id`, `sketchup`.`quotation_type` AS `autobooks_import_sketchup_data_quotation_type`, `sketchup`.`quotation_vendor_id` AS `autobooks_import_sketchup_data_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `autobooks_import_sketchup_data_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `autobooks_import_sketchup_data_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `autobooks_import_sketchup_data_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `autobooks_import_sketchup_data_quotation_due_date`, `sketchup`.`flaer_phase` AS `autobooks_import_sketchup_data_flaer_phase`, `sketchup`.`updated` AS `autobooks_import_sketchup_data_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-17 21:42:57] [data_source_manager.class.php:599] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number`, `sketchup`.`vendor_name` AS `autobooks_import_sketchup_data_vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `autobooks_import_sketchup_data_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `autobooks_import_sketchup_data_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `autobooks_import_sketchup_data_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `autobooks_import_sketchup_data_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `autobooks_import_sketchup_data_end_customer_address_3`, `sketchup`.`end_customer_city` AS `autobooks_import_sketchup_data_end_customer_city`, `sketchup`.`end_customer_state` AS `autobooks_import_sketchup_data_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `autobooks_import_sketchup_data_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `autobooks_import_sketchup_data_end_customer_country`, `sketchup`.`end_customer_account_type` AS `autobooks_import_sketchup_data_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `autobooks_import_sketchup_data_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `autobooks_import_sketchup_data_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `autobooks_import_sketchup_data_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `autobooks_import_sketchup_data_agreement_terms`, `sketchup`.`agreement_type` AS `autobooks_import_sketchup_data_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `autobooks_import_sketchup_data_agreement_support_level`, `sketchup`.`agreement_days_due` AS `autobooks_import_sketchup_data_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `autobooks_import_sketchup_data_agreement_autorenew`, `sketchup`.`product_name` AS `autobooks_import_sketchup_data_product_name`, `sketchup`.`product_family` AS `autobooks_import_sketchup_data_product_family`, `sketchup`.`product_market_segment` AS `autobooks_import_sketchup_data_product_market_segment`, `sketchup`.`product_release` AS `autobooks_import_sketchup_data_product_release`, `sketchup`.`product_type` AS `autobooks_import_sketchup_data_product_type`, `sketchup`.`product_deployment` AS `autobooks_import_sketchup_data_product_deployment`, `sketchup`.`product_sku` AS `autobooks_import_sketchup_data_product_sku`, `sketchup`.`product_sku_description` AS `autobooks_import_sketchup_data_product_sku_description`, `sketchup`.`product_part` AS `autobooks_import_sketchup_data_product_part`, `sketchup`.`product_list_price` AS `autobooks_import_sketchup_data_product_list_price`, `sketchup`.`product_list_price_currency` AS `autobooks_import_sketchup_data_product_list_price_currency`, `sketchup`.`subscription_id` AS `autobooks_import_sketchup_data_subscription_id`, `sketchup`.`subscription_serial_number` AS `autobooks_import_sketchup_data_subscription_serial_number`, `sketchup`.`subscription_status` AS `autobooks_import_sketchup_data_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `autobooks_import_sketchup_data_subscription_start_date`, `sketchup`.`subscription_end_date` AS `autobooks_import_sketchup_data_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `autobooks_import_sketchup_data_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `autobooks_import_sketchup_data_subscription_contact_email`, `sketchup`.`subscription_level` AS `autobooks_import_sketchup_data_subscription_level`, `sketchup`.`subscription_days_due` AS `autobooks_import_sketchup_data_subscription_days_due`, `sketchup`.`quotation_id` AS `autobooks_import_sketchup_data_quotation_id`, `sketchup`.`quotation_type` AS `autobooks_import_sketchup_data_quotation_type`, `sketchup`.`quotation_vendor_id` AS `autobooks_import_sketchup_data_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `autobooks_import_sketchup_data_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `autobooks_import_sketchup_data_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `autobooks_import_sketchup_data_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `autobooks_import_sketchup_data_quotation_due_date`, `sketchup`.`flaer_phase` AS `autobooks_import_sketchup_data_flaer_phase`, `sketchup`.`updated` AS `autobooks_import_sketchup_data_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-17 22:39:14] [data_source_manager.class.php:604] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number`, `sketchup`.`vendor_name` AS `autobooks_import_sketchup_data_vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `autobooks_import_sketchup_data_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `autobooks_import_sketchup_data_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `autobooks_import_sketchup_data_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `autobooks_import_sketchup_data_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `autobooks_import_sketchup_data_end_customer_address_3`, `sketchup`.`end_customer_city` AS `autobooks_import_sketchup_data_end_customer_city`, `sketchup`.`end_customer_state` AS `autobooks_import_sketchup_data_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `autobooks_import_sketchup_data_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `autobooks_import_sketchup_data_end_customer_country`, `sketchup`.`end_customer_account_type` AS `autobooks_import_sketchup_data_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `autobooks_import_sketchup_data_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `autobooks_import_sketchup_data_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `autobooks_import_sketchup_data_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `autobooks_import_sketchup_data_agreement_terms`, `sketchup`.`agreement_type` AS `autobooks_import_sketchup_data_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `autobooks_import_sketchup_data_agreement_support_level`, `sketchup`.`agreement_days_due` AS `autobooks_import_sketchup_data_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `autobooks_import_sketchup_data_agreement_autorenew`, `sketchup`.`product_name` AS `autobooks_import_sketchup_data_product_name`, `sketchup`.`product_family` AS `autobooks_import_sketchup_data_product_family`, `sketchup`.`product_market_segment` AS `autobooks_import_sketchup_data_product_market_segment`, `sketchup`.`product_release` AS `autobooks_import_sketchup_data_product_release`, `sketchup`.`product_type` AS `autobooks_import_sketchup_data_product_type`, `sketchup`.`product_deployment` AS `autobooks_import_sketchup_data_product_deployment`, `sketchup`.`product_sku` AS `autobooks_import_sketchup_data_product_sku`, `sketchup`.`product_sku_description` AS `autobooks_import_sketchup_data_product_sku_description`, `sketchup`.`product_part` AS `autobooks_import_sketchup_data_product_part`, `sketchup`.`product_list_price` AS `autobooks_import_sketchup_data_product_list_price`, `sketchup`.`product_list_price_currency` AS `autobooks_import_sketchup_data_product_list_price_currency`, `sketchup`.`subscription_id` AS `autobooks_import_sketchup_data_subscription_id`, `sketchup`.`subscription_serial_number` AS `autobooks_import_sketchup_data_subscription_serial_number`, `sketchup`.`subscription_status` AS `autobooks_import_sketchup_data_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `autobooks_import_sketchup_data_subscription_start_date`, `sketchup`.`subscription_end_date` AS `autobooks_import_sketchup_data_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `autobooks_import_sketchup_data_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `autobooks_import_sketchup_data_subscription_contact_email`, `sketchup`.`subscription_level` AS `autobooks_import_sketchup_data_subscription_level`, `sketchup`.`subscription_days_due` AS `autobooks_import_sketchup_data_subscription_days_due`, `sketchup`.`quotation_id` AS `autobooks_import_sketchup_data_quotation_id`, `sketchup`.`quotation_type` AS `autobooks_import_sketchup_data_quotation_type`, `sketchup`.`quotation_vendor_id` AS `autobooks_import_sketchup_data_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `autobooks_import_sketchup_data_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `autobooks_import_sketchup_data_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `autobooks_import_sketchup_data_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `autobooks_import_sketchup_data_quotation_due_date`, `sketchup`.`flaer_phase` AS `autobooks_import_sketchup_data_flaer_phase`, `sketchup`.`updated` AS `autobooks_import_sketchup_data_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-17 22:40:24] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:348] Generated alias: sketchup.sold_to_name => company_name (table alias: sketchup)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: vendor_name -> company_name (score: 74) loses to sold_to_name (score: 74)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:348] Generated alias: sketchup.reseller_number => reseller_name (table alias: sketchup)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: reseller_vendor_id -> reseller_name (score: 63.3) loses to reseller_number (score: 63.3)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_vendor_id -> company_name (score: 65.8) loses to sold_to_name (score: 74)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:323] Alias conflict: end_customer_name -> company_name (score: 77) replaces sold_to_name (score: 74)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:348] Generated alias: sketchup.end_customer_name => company_name (table alias: sketchup)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_address_1 -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_address_2 -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_address_3 -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_city -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_state -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_zip_code -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_country -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_account_type -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_contact_name -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:348] Generated alias: sketchup.end_customer_contact_email => email (table alias: sketchup)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_contact_phone -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: end_customer_industry_segment -> company_name (score: 65.8) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:348] Generated alias: sketchup.agreement_program_name => product_name (table alias: sketchup)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:348] Generated alias: sketchup.agreement_number => subscription_reference (table alias: sketchup)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:348] Generated alias: sketchup.agreement_start_date => start_date (table alias: sketchup)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:348] Generated alias: sketchup.agreement_end_date => end_date (table alias: sketchup)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:348] Generated alias: sketchup.agreement_status => status (table alias: sketchup)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_name -> product_name (score: 79) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_family -> product_name (score: 79) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_market_segment -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_release -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_type -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_deployment -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_sku -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_sku_description -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_part -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_list_price -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: product_list_price_currency -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: subscription_id -> subscription_reference (score: 85) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: subscription_serial_number -> subscription_reference (score: 79) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: subscription_status -> status (score: 58) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:348] Generated alias: sketchup.subscription_quantity => quantity (table alias: sketchup)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: subscription_start_date -> start_date (score: 58) loses to agreement_start_date (score: 58)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: subscription_end_date -> end_date (score: 58) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: subscription_contact_name -> company_name (score: 63.4) loses to end_customer_name (score: 77)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: subscription_contact_email -> email (score: 82) loses to end_customer_contact_email (score: 82)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: quotation_status -> status (score: 52) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:325] Alias conflict: quotation_due_date -> end_date (score: 52) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:187] Generated 10 column aliases: {"sketchup.sold_to_name":"company_name","sketchup.reseller_number":"reseller_name","sketchup.end_customer_name":"company_name","sketchup.end_customer_contact_email":"email","sketchup.agreement_program_name":"product_name","sketchup.agreement_number":"subscription_reference","sketchup.agreement_start_date":"start_date","sketchup.agreement_end_date":"end_date","sketchup.agreement_status":"status","sketchup.subscription_quantity":"quantity"}
[data_source_manager] [2025-08-17 22:43:42] [data_source_manager.class.php:267] Created data source: CSV Import: Sketchup with ID: 62
[data_source_manager] [2025-08-17 22:43:45] [data_source_manager.class.php:610] Executing query: SELECT `sketchup`.`sold_to_name` AS `autobooks_import_sketchup_data_sold_to_name`, `sketchup`.`sold_to_number` AS `autobooks_import_sketchup_data_sold_to_number`, `sketchup`.`vendor_name` AS `autobooks_import_sketchup_data_vendor_name`, `sketchup`.`reseller_number` AS `autobooks_import_sketchup_data_reseller_number`, `sketchup`.`reseller_vendor_id` AS `autobooks_import_sketchup_data_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `autobooks_import_sketchup_data_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `autobooks_import_sketchup_data_end_customer_name`, `sketchup`.`end_customer_address_1` AS `autobooks_import_sketchup_data_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `autobooks_import_sketchup_data_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `autobooks_import_sketchup_data_end_customer_address_3`, `sketchup`.`end_customer_city` AS `autobooks_import_sketchup_data_end_customer_city`, `sketchup`.`end_customer_state` AS `autobooks_import_sketchup_data_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `autobooks_import_sketchup_data_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `autobooks_import_sketchup_data_end_customer_country`, `sketchup`.`end_customer_account_type` AS `autobooks_import_sketchup_data_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `autobooks_import_sketchup_data_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `autobooks_import_sketchup_data_end_customer_contact_email`, `sketchup`.`end_customer_contact_phone` AS `autobooks_import_sketchup_data_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `autobooks_import_sketchup_data_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `autobooks_import_sketchup_data_agreement_program_name`, `sketchup`.`agreement_number` AS `autobooks_import_sketchup_data_agreement_number`, `sketchup`.`agreement_start_date` AS `autobooks_import_sketchup_data_agreement_start_date`, `sketchup`.`agreement_end_date` AS `autobooks_import_sketchup_data_agreement_end_date`, `sketchup`.`agreement_terms` AS `autobooks_import_sketchup_data_agreement_terms`, `sketchup`.`agreement_type` AS `autobooks_import_sketchup_data_agreement_type`, `sketchup`.`agreement_status` AS `autobooks_import_sketchup_data_agreement_status`, `sketchup`.`agreement_support_level` AS `autobooks_import_sketchup_data_agreement_support_level`, `sketchup`.`agreement_days_due` AS `autobooks_import_sketchup_data_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `autobooks_import_sketchup_data_agreement_autorenew`, `sketchup`.`product_name` AS `autobooks_import_sketchup_data_product_name`, `sketchup`.`product_family` AS `autobooks_import_sketchup_data_product_family`, `sketchup`.`product_market_segment` AS `autobooks_import_sketchup_data_product_market_segment`, `sketchup`.`product_release` AS `autobooks_import_sketchup_data_product_release`, `sketchup`.`product_type` AS `autobooks_import_sketchup_data_product_type`, `sketchup`.`product_deployment` AS `autobooks_import_sketchup_data_product_deployment`, `sketchup`.`product_sku` AS `autobooks_import_sketchup_data_product_sku`, `sketchup`.`product_sku_description` AS `autobooks_import_sketchup_data_product_sku_description`, `sketchup`.`product_part` AS `autobooks_import_sketchup_data_product_part`, `sketchup`.`product_list_price` AS `autobooks_import_sketchup_data_product_list_price`, `sketchup`.`product_list_price_currency` AS `autobooks_import_sketchup_data_product_list_price_currency`, `sketchup`.`subscription_id` AS `autobooks_import_sketchup_data_subscription_id`, `sketchup`.`subscription_serial_number` AS `autobooks_import_sketchup_data_subscription_serial_number`, `sketchup`.`subscription_status` AS `autobooks_import_sketchup_data_subscription_status`, `sketchup`.`subscription_quantity` AS `autobooks_import_sketchup_data_subscription_quantity`, `sketchup`.`subscription_start_date` AS `autobooks_import_sketchup_data_subscription_start_date`, `sketchup`.`subscription_end_date` AS `autobooks_import_sketchup_data_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `autobooks_import_sketchup_data_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `autobooks_import_sketchup_data_subscription_contact_email`, `sketchup`.`subscription_level` AS `autobooks_import_sketchup_data_subscription_level`, `sketchup`.`subscription_days_due` AS `autobooks_import_sketchup_data_subscription_days_due`, `sketchup`.`quotation_id` AS `autobooks_import_sketchup_data_quotation_id`, `sketchup`.`quotation_type` AS `autobooks_import_sketchup_data_quotation_type`, `sketchup`.`quotation_vendor_id` AS `autobooks_import_sketchup_data_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `autobooks_import_sketchup_data_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `autobooks_import_sketchup_data_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `autobooks_import_sketchup_data_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `autobooks_import_sketchup_data_quotation_due_date`, `sketchup`.`flaer_phase` AS `autobooks_import_sketchup_data_flaer_phase`, `sketchup`.`updated` AS `autobooks_import_sketchup_data_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-17 22:52:31] [data_source_manager.class.php:610] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `sketchup_vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `sketchup_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `sketchup_end_customer_city`, `sketchup`.`end_customer_state` AS `sketchup_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `sketchup_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `sketchup_end_customer_country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `sketchup_product_family`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `sketchup_product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `sketchup_subscription_serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-17 22:52:32] [data_source_manager.class.php:610] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `sketchup_vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `sketchup_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `sketchup_end_customer_city`, `sketchup`.`end_customer_state` AS `sketchup_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `sketchup_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `sketchup_end_customer_country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `sketchup_product_family`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `sketchup_product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `sketchup_subscription_serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-18 08:03:20] [data_source_manager.class.php:634] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `sketchup_vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `sketchup_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `sketchup_end_customer_city`, `sketchup`.`end_customer_state` AS `sketchup_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `sketchup_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `sketchup_end_customer_country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `sketchup_product_family`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `sketchup_product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `sketchup_subscription_serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-18 08:09:31] [data_source_manager.class.php:634] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `sketchup_vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `sketchup_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `sketchup_end_customer_city`, `sketchup`.`end_customer_state` AS `sketchup_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `sketchup_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `sketchup_end_customer_country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `sketchup_product_family`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `sketchup_product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `sketchup_subscription_serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-18 08:30:38] [data_source_manager.class.php:267] Created data source: Autodesk_products with ID: 63
[data_source_manager] [2025-08-18 08:31:04] [data_source_manager.class.php:634] Executing query: SELECT * FROM `products_autodesk_catalog` LIMIT 50
[data_source_manager] [2025-08-18 09:05:58] [data_source_manager.class.php:634] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `sketchup_vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `sketchup_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `sketchup_end_customer_city`, `sketchup`.`end_customer_state` AS `sketchup_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `sketchup_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `sketchup_end_customer_country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `sketchup_product_family`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `sketchup_product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `sketchup_subscription_serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-18 13:00:03] [data_source_manager.class.php:634] Executing query: SELECT * FROM `products_autodesk_catalog` LIMIT 1
[data_source_manager] [2025-08-18 13:00:03] [data_source_manager.class.php:634] Executing query: SELECT * FROM `products_autodesk_catalog`
[data_source_manager] [2025-08-18 13:03:47] [data_source_manager.class.php:634] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-18 13:03:54] [data_source_manager.class.php:634] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `sketchup_vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `sketchup_end_customer_address_1`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `sketchup_end_customer_city`, `sketchup`.`end_customer_state` AS `sketchup_end_customer_state`, `sketchup`.`end_customer_zip_code` AS `sketchup_end_customer_zip_code`, `sketchup`.`end_customer_country` AS `sketchup_end_customer_country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `sketchup_product_family`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `sketchup_product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `sketchup_subscription_serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-18 13:04:00] [data_source_manager.class.php:634] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-18 13:04:19] [data_source_manager.class.php:634] Executing query: SELECT * FROM `products_autodesk_catalog` LIMIT 1
[data_source_manager] [2025-08-18 13:04:19] [data_source_manager.class.php:634] Executing query: SELECT * FROM `products_autodesk_catalog`
[data_source_manager] [2025-08-18 13:05:29] [data_source_manager.class.php:634] Executing query: SELECT * FROM `products_autodesk_catalog` ORDER BY `last_modified` ASC
[data_source_manager] [2025-08-18 13:05:35] [data_source_manager.class.php:634] Executing query: SELECT * FROM `products_autodesk_catalog` ORDER BY `last_modified` ASC
[data_source_manager] [2025-08-18 13:05:37] [data_source_manager.class.php:634] Executing query: SELECT * FROM `products_autodesk_catalog` ORDER BY `created_at` ASC
[data_source_manager] [2025-08-18 13:05:39] [data_source_manager.class.php:634] Executing query: SELECT * FROM `products_autodesk_catalog` ORDER BY `created_at` ASC
[data_source_manager] [2025-08-18 13:05:40] [data_source_manager.class.php:634] Executing query: SELECT * FROM `products_autodesk_catalog` ORDER BY `last_modified` ASC
