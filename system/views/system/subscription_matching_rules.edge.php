{{--
/**
 * Subscription Matching Rules Manager - Edge Template
 */
--}}
@props([
    'title' => 'Subscription Matching Rules',
    'description' => 'Configure how the system matches CSV subscription data with Autodesk customers',
    'rules' => subscription_matching_rules::get_rules(),
    'stats' => subscription_matching_rules::get_rule_statistics(),
    'all_fields' => unified_field_definitions::get_all_fields(),
    'categories' => unified_field_definitions::get_categories(),
])

@use system\subscription_matching_rules;
@use system\unified_field_definitions;


<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Subscription Matching Rules</h1>
                <p class="mt-2 text-sm text-gray-600">Configure how the system matches CSV subscription data with Autodesk customers</p>
            </div>
            <div class="flex space-x-3">
                <button type="button" 
                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        hx-post="{{ APP_ROOT }}/api/subscription_matching_rules/reset_defaults"
                        hx-confirm="Are you sure you want to reset all rules to defaults? This will overwrite any custom configurations."
                        hx-target="#rules-container"
                        hx-indicator="#loading-indicator">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset to Defaults
                </button>
                <button type="button"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
                        hx-post="{{ APP_ROOT }}/api/subscription_matching_rules/test"
                        hx-target="#test-results"
                        hx-indicator="#loading-indicator">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Test Rules
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator">
        <div class="flex items-center justify-center py-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <span class="ml-2 text-sm text-gray-600">Processing...</span>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Rules</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ count($rules) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Rules</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['active_rules'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">High Priority</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['high_priority_rules'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Custom Rules</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['custom_rules'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Field Definitions -->
    <x-field-definitions-section />

    <!-- Rules List -->
    <div id="rules-container" class="space-y-6">
        @foreach($rules as $rule_name => $rule)
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <h3 class="text-lg font-medium text-gray-900">
                                {{ ucwords(str_replace('_', ' ', $rule_name)) }}
                            </h3>
                            @if($rule['enabled'] ?? false)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Inactive
                                </span>
                            @endif
                        </div>
                        <div class="flex items-center space-x-2">
                            <button type="button"
                                    class="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                    hx-get="{{ APP_ROOT }}/api/subscription_matching_rules/edit?rule={{ urlencode($rule_name) }}"
                                    hx-target="#rule-editor"
                                    hx-indicator="#loading-indicator">
                                Edit
                            </button>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" 
                                       class="sr-only peer"
                                       {{ ($rule['enabled'] ?? false) ? 'checked' : '' }}
                                       hx-post="{{ APP_ROOT }}/api/subscription_matching_rules/toggle"
                                       hx-vals='{"rule_name": "{{ $rule_name }}", "enabled": "{{ ($rule['enabled'] ?? false) ? 'false' : 'true' }}"}'
                                       hx-target="#rules-container"
                                       hx-indicator="#loading-indicator">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Priority</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $rule['priority'] ?? 'Not set' }}</dd>
                        </div>
                        @if(isset($rule['field_patterns']))
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Field Patterns</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    @if(is_array($rule['field_patterns']))
                                        {{ implode(', ', array_slice($rule['field_patterns'], 0, 3)) }}
                                        @if(count($rule['field_patterns']) > 3)
                                            <span class="text-gray-500">+{{ count($rule['field_patterns']) - 3 }} more</span>
                                        @endif
                                    @else
                                        {{ $rule['field_patterns'] }}
                                    @endif
                                </dd>
                            </div>
                        @endif
                        @if(isset($rule['similarity_threshold']))
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Similarity Threshold</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $rule['similarity_threshold'] }}%</dd>
                            </div>
                        @endif
                        @if(isset($rule['confidence_score']))
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Confidence Score</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $rule['confidence_score'] }}%</dd>
                            </div>
                        @endif
                        @if(isset($rule['case_sensitive']))
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Case Sensitive</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $rule['case_sensitive'] ? 'Yes' : 'No' }}</dd>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Rule Editor Modal Placeholder -->
    <div id="rule-editor"></div>
    
    <!-- Test Results Placeholder -->
    <div id="test-results" class="mt-8"></div>
</div>
