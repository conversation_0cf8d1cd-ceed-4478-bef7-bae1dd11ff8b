<?php
/**
 * Admin API Router
 * Routes admin-related API calls to appropriate handlers
 */

// Ensure user has admin access
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied']);
    exit;
}

// Get the sub-route from the URL
$path_parts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
$admin_route = '';

// Find the admin route part
$found_admin = false;
foreach ($path_parts as $part) {
    if ($found_admin) {
        $admin_route = $part;
        break;
    }
    if ($part === 'admin') {
        $found_admin = true;
    }
}

// Route to appropriate handler
switch ($admin_route) {
    case 'subscription_matching_rules':
        include __DIR__ . '/admin/subscription_matching_rules.api.php';
        break;
        
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Admin API endpoint not found: ' . $admin_route]);
        break;
}
?>
